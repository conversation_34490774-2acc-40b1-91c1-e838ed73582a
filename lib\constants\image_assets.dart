/// 图片资源常量
class ImageAssets {
  // 产品图片
  static const String productCoffee1 = 'assets/images/productCoffee/productCoffee1.jpg';
  static const String productCoffee2 = 'assets/images/productCoffee/productCoffee2.jpg';
  static const String productCoffee3 = 'assets/images/productCoffee/productCoffee3.jpg';
  static const String productCoffee4 = 'assets/images/productCoffee/productCoffee4.jpg';
  static const String productCoffee5 = 'assets/images/productCoffee/productCoffee5.jpg';
  static const String productCoffee6 = 'assets/images/productCoffee/productCoffee6.jpg';
  static const String productCoffee7 = 'assets/images/productCoffee/productCoffee7.jpg';
  static const String productCoffee8 = 'assets/images/productCoffee/productCoffee8.jpg';

  // 茶类饮品
  static const String productTea1 = 'assets/images/productTea/productTea1.jpg';
  static const String productTea2 = 'assets/images/productTea/productTea2.jpg';
  static const String productTea3 = 'assets/images/productTea/productTea3.jpg';
  static const String productTea4 = 'assets/images/productTea/productTea4.jpg';

  // 甜品
  static const String productDessert1 = 'assets/images/productDessert/productDessert1.jpg';
  static const String productDessert2 = 'assets/images/productDessert/productDessert2.jpg';
  static const String productDessert3 = 'assets/images/productDessert/productDessert3.jpg';
  static const String productDessert4 = 'assets/images/productDessert/productDessert4.jpg';

  // 轻食
  static const String productSnack1 = 'assets/images/productSnack/productSnack1.jpg';
  static const String productSnack2 = 'assets/images/productSnack/productSnack2.jpg';
  static const String productSnack3 = 'assets/images/productSnack/productSnack3.jpg';
  static const String productSnack4 = 'assets/images/productSnack/productSnack4.jpg';

  // 用户头像
  static const String avatar1 = 'assets/images/avatar/avatar1.jpg';
  static const String avatar2 = 'assets/images/avatar/avatar2.jpg';
  static const String avatar3 = 'assets/images/avatar/avatar3.jpg';
  static const String avatar4 = 'assets/images/avatar/avatar4.jpg';
  static const String avatar5 = 'assets/images/avatar/avatar5.jpg';
  static const String avatar6 = 'assets/images/avatar/avatar6.jpg';

  // 活动图片
  static const String activity1 = 'assets/images/activity/activity1.jpg';
  static const String activity2 = 'assets/images/activity/activity2.jpg';
  static const String activity3 = 'assets/images/activity/activity3.jpg';
  static const String activity4 = 'assets/images/activity/activity4.jpg';

  // 社区图片
  static const String community1 = 'assets/images/community/community1.jpg';
  static const String community2 = 'assets/images/community/community2.jpg';
  static const String community3 = 'assets/images/community/community3.jpg';
  static const String community4 = 'assets/images/community/community4.jpg';

  // 默认头像
  static const String defaultAvatar = 'assets/images/defaultAvatar/defaultAvatar.jpg';

  // 默认产品图片
  static const String defaultProduct = 'assets/images/defaultProduct/defaultProduct.jpg';
  
  // 产品图片列表
  static const List<String> coffeeImages = [
    productCoffee1,
    productCoffee2,
    productCoffee3,
    productCoffee4,
    productCoffee5,
    productCoffee6,
    productCoffee7,
    productCoffee8,
  ];
  
  static const List<String> teaImages = [
    productTea1,
    productTea2,
    productTea3,
    productTea4,
  ];
  
  static const List<String> dessertImages = [
    productDessert1,
    productDessert2,
    productDessert3,
    productDessert4,
  ];
  
  static const List<String> snackImages = [
    productSnack1,
    productSnack2,
    productSnack3,
    productSnack4,
  ];
  
  static const List<String> avatarImages = [
    avatar1,
    avatar2,
    avatar3,
    avatar4,
    avatar5,
    avatar6,
  ];
  
  static const List<String> activityImages = [
    activity1,
    activity2,
    activity3,
    activity4,
  ];
  
  static const List<String> communityImages = [
    community1,
    community2,
    community3,
    community4,
  ];
  
  /// 根据分类获取产品图片
  static List<String> getProductImagesByCategory(String category) {
    switch (category.toLowerCase()) {
      case '咖啡':
      case 'coffee':
        return coffeeImages;
      case '茶饮':
      case 'tea':
        return teaImages;
      case '甜品':
      case 'dessert':
        return dessertImages;
      case '轻食':
      case 'snack':
        return snackImages;
      default:
        return coffeeImages;
    }
  }
  
  /// 获取随机头像
  static String getRandomAvatar() {
    return avatarImages[(DateTime.now().millisecondsSinceEpoch % avatarImages.length)];
  }
  
  /// 获取随机产品图片
  static String getRandomProductImage() {
    final allImages = [...coffeeImages, ...teaImages, ...dessertImages, ...snackImages];
    return allImages[(DateTime.now().millisecondsSinceEpoch % allImages.length)];
  }
}
