import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../services/app_service.dart';
import '../pages/home_page.dart';
import '../pages/menu_page.dart';
import '../pages/ai_home_page.dart';
import '../pages/community_page.dart';
import '../pages/profile_page.dart';

/// 主脚手架，包含底部导航栏
class MainScaffold extends StatefulWidget {
  const MainScaffold({super.key});

  @override
  State<MainScaffold> createState() => _MainScaffoldState();
}

class _MainScaffoldState extends State<MainScaffold> {
  late PageController _pageController;
  
  // 页面列表
  final List<Widget> _pages = [
    const HomePage(),
    const MenuPage(),
    const AIHomePage(),
    const CommunityPage(),
    const ProfilePage(),
  ];

  @override
  void initState() {
    super.initState();
    final appService = context.read<AppService>();
    _pageController = PageController(initialPage: appService.currentTabIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onTabTapped(int index) {
    final appService = context.read<AppService>();
    appService.setCurrentTabIndex(index);
    _pageController.animateToPage(
      index,
      duration: AppConstants.animationDurationMedium,
      curve: Curves.easeInOut,
    );
  }

  Widget _buildCustomBottomNavigationBar(AppService appService) {
    return Container(
      height: 70,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: TabBarConfig.tabs.asMap().entries.map((entry) {
          final index = entry.key;
          final tab = entry.value;
          final isSelected = index == appService.currentTabIndex;

          // 特殊处理"小盛"按钮（索引为2）
          if (index == 2) {
            return GestureDetector(
              onTap: () => _onTabTapped(index),
              child: Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: isSelected
                        ? [AppConstants.primaryColor, AppConstants.primaryColor.withValues(alpha: 0.8)]
                        : [Colors.grey[300]!, Colors.grey[400]!],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: isSelected
                          ? AppConstants.primaryColor.withValues(alpha: 0.3)
                          : Colors.grey.withValues(alpha: 0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Icon(
                  isSelected ? tab['activeIcon'] : tab['icon'],
                  color: Colors.white,
                  size: 28,
                ),
              ),
            );
          }

          // 普通导航按钮
          return GestureDetector(
            onTap: () => _onTabTapped(index),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    isSelected ? tab['activeIcon'] : tab['icon'],
                    color: isSelected ? AppConstants.primaryColor : Colors.grey[600],
                    size: 24,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    tab['label'],
                    style: TextStyle(
                      fontSize: 12,
                      color: isSelected ? AppConstants.primaryColor : Colors.grey[600],
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppService>(
      builder: (context, appService, child) {
        return Scaffold(
          body: PageView(
            controller: _pageController,
            children: _pages,
            onPageChanged: (index) {
              appService.setCurrentTabIndex(index);
            },
          ),
          bottomNavigationBar: _buildCustomBottomNavigationBar(appService),
        );
      },
    );
  }
}
