# 新易盛咖啡馆移动端应用

## 项目简介

新易盛咖啡馆移动端应用是一个基于 uni-app 框架开发的跨平台移动应用，旨在为新易盛员工提供便捷的咖啡馆服务体验。应用集成了在线点餐、社区交流、活动参与等核心功能，特别突出了以推特风格呈现的社区功能。

## 核心功能

### 🏠 首页
- 轮播图展示
- 快捷功能入口
- 今日推荐商品
- 热门社区动态
- 即将到来的活动

### ☕ 菜单
- 商品分类浏览
- 搜索功能
- 商品详情展示
- 购物车管理
- 在线下单

### 📋 订单
- 订单状态跟踪
- 订单历史查看
- 支付管理
- 评价功能

### 🌟 社区（推特风格）
- 动态发布与浏览
- 图片分享
- 点赞、评论、转发
- 话题标签
- 位置分享
- 隐私设置

### 👤 个人中心
- 用户信息管理
- 订单快捷入口
- 积分与优惠券
- 设置选项

## 技术栈

- **框架**: uni-app 3.x
- **前端**: Vue 3 + JavaScript
- **样式**: CSS3 + Flexbox
- **构建工具**: Vite
- **包管理**: npm

## 项目结构

```
XYS_Coffee_App/
├── .trae/                    # Trae IDE 配置
├── common/                   # 公共资源
│   ├── css/                 # 全局样式
│   │   └── common.css       # 通用样式文件
│   └── data/                # 模拟数据
│       └── mockData.js      # 模拟数据文件
├── docs/                    # 项目文档
│   ├── 新易盛咖啡馆需求实现方案.md
│   └── 新易盛咖啡系统.png
├── pages/                   # 页面文件
│   ├── index/              # 首页
│   │   └── index.nvue
│   ├── menu/               # 菜单页面
│   │   └── menu.nvue
│   ├── order/              # 订单页面
│   │   └── order.nvue
│   ├── community/          # 社区页面
│   │   ├── community.nvue  # 社区首页
│   │   ├── post-detail.nvue # 动态详情
│   │   └── publish.nvue    # 发布动态
│   └── profile/            # 个人中心
│       └── profile.nvue
├── App.vue                  # 应用主文件
├── main.js                  # 应用入口文件
├── manifest.json            # 应用配置文件
├── pages.json              # 页面路由配置
├── package.json            # 项目依赖配置
└── README.md               # 项目说明文档
```

## 安装与运行

### 环境要求

- Node.js >= 14.0.0
- npm >= 6.0.0
- HBuilderX 或其他支持 uni-app 的开发工具

### 安装依赖

```bash
npm install
```

### 开发运行

#### H5 开发
```bash
npm run dev:h5
```

#### 微信小程序开发
```bash
npm run dev:mp-weixin
```

#### App 开发
```bash
npm run dev:app-plus
```

### 生产构建

#### H5 构建
```bash
npm run build:h5
```

#### 微信小程序构建
```bash
npm run build:mp-weixin
```

#### App 构建
```bash
npm run build:app-plus
```

## 功能特色

### 社区功能（推特风格）

本应用的社区功能采用推特风格设计，提供了丰富的社交体验：

- **动态发布**: 支持文字、图片、位置、话题标签
- **互动功能**: 点赞、评论、转发
- **隐私控制**: 可设置动态可见范围（全部、同部门、同园区）
- **实时更新**: 模拟实时动态流
- **用户体验**: 流畅的滚动和交互动画

### 数据模拟

由于项目暂不开发后端，所有数据均通过 `mockData.js` 文件模拟：

- 用户信息
- 社区动态
- 商品菜单
- 订单数据
- 活动信息

## 设计理念

### 用户体验
- 简洁直观的界面设计
- 流畅的交互动画
- 响应式布局适配
- 无障碍访问支持

### 视觉设计
- 咖啡主题色彩搭配
- 现代化的 UI 组件
- 一致的视觉语言
- 品牌元素融入

### 性能优化
- 组件懒加载
- 图片压缩优化
- 代码分割
- 缓存策略

## 开发规范

### 代码规范
- 使用 ES6+ 语法
- 组件化开发
- 统一的命名规范
- 详细的注释说明

### 文件命名
- 页面文件：kebab-case
- 组件文件：PascalCase
- 样式文件：kebab-case
- 资源文件：kebab-case

### Git 提交规范
- feat: 新功能
- fix: 修复问题
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 部署说明

### H5 部署
1. 执行 `npm run build:h5`
2. 将 `dist/build/h5` 目录部署到 Web 服务器

### 小程序部署
1. 执行 `npm run build:mp-weixin`
2. 使用微信开发者工具打开 `dist/build/mp-weixin` 目录
3. 上传代码到微信小程序平台

### App 部署
1. 执行 `npm run build:app-plus`
2. 使用 HBuilderX 打开项目
3. 配置原生插件和证书
4. 打包生成 APK/IPA 文件

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系我们

- 项目维护者：XYS Coffee Team
- 邮箱：<EMAIL>
- 项目地址：https://github.com/xys-coffee/mobile-app

## 更新日志

### v1.0.0 (2024-01-01)
- 🎉 初始版本发布
- ✨ 完成核心功能开发
- 🎨 实现推特风格社区功能
- 📱 支持多平台部署

---

**感谢使用新易盛咖啡馆移动端应用！** ☕️