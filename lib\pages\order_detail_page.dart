import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/order.dart';
import '../utils/app_utils.dart';
import '../utils/date_utils.dart' as app_date;

/// 订单详情页面
class OrderDetailPage extends StatefulWidget {
  final Order order;

  const OrderDetailPage({
    super.key,
    required this.order,
  });

  @override
  State<OrderDetailPage> createState() => _OrderDetailPageState();
}

class _OrderDetailPageState extends State<OrderDetailPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: Text('订单详情 #${widget.order.id}'),
        backgroundColor: Colors.white,
        foregroundColor: AppConstants.textPrimary,
        elevation: 0,
        actions: [
          if (widget.order.status == 'pending')
            TextButton(
              onPressed: _cancelOrder,
              child: const Text(
                '取消订单',
                style: TextStyle(color: AppConstants.errorColor),
              ),
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 订单状态
            _buildOrderStatus(),
            const SizedBox(height: AppConstants.paddingLarge),
            
            // 商品信息
            _buildOrderItems(),
            const SizedBox(height: AppConstants.paddingLarge),
            
            // 订单信息
            _buildOrderInfo(),
            const SizedBox(height: AppConstants.paddingLarge),
            
            // 配送信息
            _buildDeliveryInfo(),
            const SizedBox(height: AppConstants.paddingLarge),
            
            // 支付信息
            _buildPaymentInfo(),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomActions(),
    );
  }

  Widget _buildOrderStatus() {
    return Container(
      width: double.infinity, // 增加宽度做整体对齐
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center, // 整体对齐
        crossAxisAlignment: CrossAxisAlignment.center, // 水平居中对齐
        children: [
          Icon(
            _getStatusIcon(),
            size: 60,
            color: _getStatusColor(),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Text(
            widget.order.statusText,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: _getStatusColor(),
            ),
            textAlign: TextAlign.center, // 文字居中对齐
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          Text(
            _getStatusDescription(),
            style: const TextStyle(
              fontSize: 14,
              color: AppConstants.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderItems() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '商品信息',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          ...widget.order.items.map((item) => _buildOrderItem(item)),
        ],
      ),
    );
  }

  Widget _buildOrderItem(OrderItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Row(
        children: [
          // 商品图片
          ClipRRect(
            borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
            child: Image.asset(
              _getProductImage(item.id),
              width: 60,
              height: 60,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                width: 60,
                height: 60,
                color: AppConstants.backgroundColor,
                child: const Icon(
                  Icons.local_cafe,
                  color: AppConstants.textLight,
                  size: 30,
                ),
              ),
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),

          // 商品信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                if (item.options.isNotEmpty)
                  Text(
                    item.options.join(' | '),
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppConstants.textSecondary,
                    ),
                  ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppUtils.formatPrice(item.price),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppConstants.primaryColor,
                      ),
                    ),
                    Text(
                      'x${item.quantity}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppConstants.textSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderInfo() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '订单信息',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          _buildInfoRow('订单编号', widget.order.id),
          _buildInfoRow('下单时间', widget.order.orderTime),
          _buildInfoRow('取餐地点', widget.order.pickupLocation),
          if (widget.order.pickupTime != null)
            _buildInfoRow('取餐时间', widget.order.pickupTime!),
          if (widget.order.estimatedTime != null)
            _buildInfoRow('预计完成', widget.order.estimatedTime!),
          const Divider(),
          _buildInfoRow(
            '订单金额',
            AppUtils.formatPrice(widget.order.totalAmount),
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? AppConstants.textPrimary : AppConstants.textSecondary,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? AppConstants.primaryColor : AppConstants.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveryInfo() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '取餐信息',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Row(
            children: [
              const Icon(
                Icons.location_on,
                color: AppConstants.primaryColor,
                size: 20,
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.order.pickupLocation,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (widget.order.pickupTime != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        '预计取餐时间：${widget.order.pickupTime}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: AppConstants.textSecondary,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentInfo() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '支付信息',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          _buildInfoRow('支付方式', '微信支付'),
          if (widget.order.status != 'pending')
            _buildInfoRow('支付时间', widget.order.orderTime),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    if (widget.order.status == 'pending') {
      return Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        decoration: const BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 10,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _cancelOrder,
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    side: const BorderSide(color: AppConstants.errorColor),
                  ),
                  child: const Text(
                    '取消订单',
                    style: TextStyle(
                      color: AppConstants.errorColor,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              Expanded(
                flex: 2,
                child: ElevatedButton(
                  onPressed: _payOrder,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppConstants.primaryColor,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: Text(
                    '立即支付 ${AppUtils.formatPrice(widget.order.totalAmount)}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else if (widget.order.status == 'delivered') {
      return Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        decoration: const BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 10,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _reorder,
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text(
                    '再来一单',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: AppConstants.paddingMedium),
              Expanded(
                child: ElevatedButton(
                  onPressed: _rateOrder,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppConstants.primaryColor,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text(
                    '评价订单',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }

  IconData _getStatusIcon() {
    switch (widget.order.status) {
      case 'pending':
        return Icons.schedule;
      case 'paid':
        return Icons.restaurant;
      case 'preparing':
        return Icons.local_cafe;
      case 'ready':
        return Icons.check_circle;
      case 'delivering':
        return Icons.delivery_dining;
      case 'delivered':
        return Icons.check_circle; // 已完成状态使用选中图标
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }

  Color _getStatusColor() {
    switch (widget.order.status) {
      case 'pending':
        return Colors.orange;
      case 'paid':
      case 'preparing':
        return AppConstants.primaryColor;
      case 'ready':
      case 'delivering':
        return Colors.blue;
      case 'delivered':
        return AppConstants.successColor;
      case 'cancelled':
        return AppConstants.errorColor;
      default:
        return AppConstants.textSecondary;
    }
  }

  String _getStatusDescription() {
    switch (widget.order.status) {
      case 'pending':
        return '请在30分钟内完成支付，超时订单将自动取消';
      case 'paid':
        return '您的订单已支付成功，我们正在为您准备';
      case 'preparing':
        return '咖啡师正在精心为您制作，请稍候';
      case 'ready':
        return '您的订单已制作完成，请及时取餐';
      case 'delivering':
        return '配送员正在为您送餐，请保持电话畅通';
      case 'delivered':
        return '订单已完成，感谢您的光临';
      case 'cancelled':
        return '订单已取消，如有疑问请联系客服';
      default:
        return '订单状态未知';
    }
  }

  void _cancelOrder() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('取消订单'),
        content: const Text('确定要取消这个订单吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: 实现取消订单逻辑
              AppUtils.showSuccess(context, '订单已取消');
              Navigator.of(context).pop();
            },
            child: const Text(
              '确定',
              style: TextStyle(color: AppConstants.errorColor),
            ),
          ),
        ],
      ),
    );
  }

  void _payOrder() {
    // TODO: 实现支付逻辑
    AppUtils.showSuccess(context, '正在跳转到支付页面...');
  }

  void _reorder() {
    // TODO: 实现再来一单逻辑
    AppUtils.showSuccess(context, '已添加到购物车');
  }

  void _rateOrder() {
    // TODO: 实现评价订单逻辑
    AppUtils.showSuccess(context, '正在跳转到评价页面...');
  }

  String _getProductImage(int productId) {
    // 根据商品ID返回对应的图片路径
    switch (productId) {
      case 1:
        return 'assets/images/productCoffee/productCoffee1.jpg';
      case 2:
        return 'assets/images/productCoffee/productCoffee2.jpg';
      case 3:
        return 'assets/images/productCoffee/productCoffee3.jpg';
      case 4:
        return 'assets/images/productCoffee/productCoffee4.jpg';
      case 5:
        return 'assets/images/productTea/productTea1.jpg';
      case 6:
        return 'assets/images/productTea/productTea2.jpg';
      case 7:
        return 'assets/images/productSnack/productSnack1.jpg';
      case 8:
        return 'assets/images/productSnack/productSnack2.jpg';
      default:
        return 'assets/images/productCoffee/productCoffee1.jpg';
    }
  }
}
