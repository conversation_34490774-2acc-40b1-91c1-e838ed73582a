import 'dart:math';
import '../models/menu.dart';
import '../models/community_post.dart';
import '../models/activity.dart';
import 'mock_data_service.dart';

/// AI服务 - 模拟AI助手响应
class AIService {
  static final AIService _instance = AIService._internal();
  factory AIService() => _instance;
  AIService._internal();

  final MockDataService _dataService = MockDataService();

  /// 获取AI响应
  Future<String> getResponse(String userInput) async {
    // 模拟网络延迟
    await Future.delayed(Duration(milliseconds: 500 + Random().nextInt(1500)));
    
    final input = userInput.toLowerCase();
    
    // 企业相关
    if (_containsAny(input, ['企业', '公司', '历史', '文化', '新易盛', '关于'])) {
      return _getCompanyInfo(input);
    }
    
    // 菜单和下单相关
    if (_containsAny(input, ['菜单', '咖啡', '饮品', '下单', '点餐', '价格', '推荐'])) {
      return _getMenuInfo(input);
    }
    
    // 优惠活动相关
    if (_containsAny(input, ['优惠', '折扣', '活动', '促销', '会员', '积分'])) {
      return _getPromotionInfo(input);
    }
    
    // 社区相关
    if (_containsAny(input, ['社区', '动态', '分享', '热点', '话题'])) {
      return _getCommunityInfo(input);
    }
    
    // 订单相关
    if (_containsAny(input, ['订单', '配送', '取餐', '退款', '查询'])) {
      return _getOrderInfo(input);
    }
    
    // 问候语
    if (_containsAny(input, ['你好', '嗨', 'hi', 'hello', '早上好', '下午好', '晚上好'])) {
      return _getGreeting();
    }
    
    // 感谢
    if (_containsAny(input, ['谢谢', '感谢', 'thanks', '谢了'])) {
      return _getThanksResponse();
    }
    
    // 默认响应
    return _getDefaultResponse();
  }

  bool _containsAny(String text, List<String> keywords) {
    return keywords.any((keyword) => text.contains(keyword));
  }

  String _getCompanyInfo(String input) {
    final responses = [
      '新易盛咖啡馆成立于2018年，致力于为顾客提供高品质的咖啡体验。我们秉承"用心做好每一杯咖啡"的理念，选用优质咖啡豆，采用精湛工艺，为您呈现醇香浓郁的咖啡。',
      '我们是一家专注于咖啡文化传播的连锁咖啡馆，目前在北京、上海、深圳等城市设有分店。我们不仅提供美味的咖啡，还定期举办咖啡文化活动，与咖啡爱好者分享交流。',
      '新易盛咖啡馆以"让每个人都能享受到专业级咖啡"为使命，我们拥有专业的咖啡师团队，严格把控每一个制作环节，确保为您提供最佳的咖啡体验。',
    ];
    return responses[Random().nextInt(responses.length)];
  }

  String _getMenuInfo(String input) {
    if (input.contains('推荐')) {
      return _getProductRecommendations();
    }

    if (input.contains('价格')) {
      return '我们的饮品价格如下：\n\n☕ 咖啡类：25-35元\n🥛 奶茶类：22-32元\n🧃 果汁类：18-28元\n🍰 轻食类：15-45元\n\n会员享受9折优惠，每周三咖啡日全场8.5折！您可以在菜单页面查看完整价格表。';
    }

    return '我们提供丰富的饮品选择，包括各式咖啡、奶茶、果汁和精美轻食。您可以点击底部"菜单"查看完整菜单，或者告诉我您的喜好，我来为您推荐合适的饮品！';
  }

  String _getProductRecommendations() {
    final categories = _dataService.getMenuCategories();
    final popularProducts = <Map<String, dynamic>>[];

    // 从每个分类中选择热门商品
    for (final category in categories) {
      final popularInCategory = category.items.where((item) => item.popular).take(2).toList();
      for (final item in popularInCategory) {
        popularProducts.add({
          'item': item,
          'categoryName': category.name,
        });
      }
    }

    if (popularProducts.isEmpty) {
      // 如果没有热门商品，选择前几个
      for (final category in categories) {
        for (final item in category.items.take(2)) {
          popularProducts.add({
            'item': item,
            'categoryName': category.name,
          });
        }
      }
    }

    String response = '我为您推荐几款人气饮品：\n\n';

    for (int i = 0; i < popularProducts.take(4).length; i++) {
      final productData = popularProducts[i];
      final product = productData['item'] as MenuItem;
      final categoryName = productData['categoryName'] as String;

      response += '${_getProductEmoji(categoryName)} ${product.name}\n';
      response += '💰 ¥${product.price.toStringAsFixed(0)}';
      if (product.originalPrice != null && product.originalPrice! > product.price) {
        response += ' (原价¥${product.originalPrice!.toStringAsFixed(0)})';
      }
      response += '\n📝 ${product.description}\n';
      response += '👆 [点击查看详情](product:${product.id})\n\n';
    }

    response += '您想了解哪款饮品的详细信息呢？点击上方链接可查看商品详情！';
    return response;
  }

  String _getProductEmoji(String categoryName) {
    switch (categoryName) {
      case '咖啡':
        return '☕';
      case '茶饮':
        return '🍃';
      case '甜品':
        return '🍰';
      case '轻食':
        return '🥪';
      default:
        return '☕';
    }
  }

  String _getPromotionInfo(String input) {
    if (input.contains('活动') || input.contains('咨询')) {
      return _getLatestActivities();
    }

    final promotions = [
      '🎉 当前优惠活动：\n\n• 新用户注册送20元优惠券\n• 会员全场9折优惠\n• 每周三咖啡日8.5折\n• 满88元免配送费\n• 积分兑换精美礼品\n\n更多优惠请关注我们的活动推送！',
      '💰 会员福利多多：\n\n• 消费积分，1元=1积分\n• 生日当月享受7折优惠\n• 专属会员活动优先参与\n• 新品免费试饮机会\n\n立即注册成为会员，享受更多专属优惠！',
      '🎁 本月特惠：\n\n• 买二送一活动（指定饮品）\n• 充值100送20优惠券\n• 邀请好友注册送积分\n• 社区分享获得优惠码\n\n优惠有限，快来参与吧！',
    ];
    return promotions[Random().nextInt(promotions.length)];
  }

  String _getLatestActivities() {
    final activities = _dataService.getActivities();
    // 简化逻辑，直接取前3个活动
    final upcomingActivities = activities.take(3).toList();

    if (upcomingActivities.isEmpty) {
      return '暂时没有新的活动，不过我们会定期推出精彩活动，请关注我们的最新动态！';
    }

    String response = '🎊 最新活动推荐：\n\n';

    for (int i = 0; i < upcomingActivities.length; i++) {
      final activity = upcomingActivities[i];
      response += '${i + 1}. ${activity.title}\n';
      response += '📅 ${activity.date} ${activity.time}\n';
      response += '📍 ${activity.location}\n';
      response += '📝 ${activity.description}\n';
      response += '👆 [点击查看详情](activity:${activity.id})\n\n';
    }

    response += '点击上方链接查看活动详情并参与！';
    return response;
  }

  String _getCommunityInfo(String input) {
    if (input.contains('热点') || input.contains('最近')) {
      return _getHotCommunityPosts();
    }

    return '☕ 社区是我们咖啡爱好者的交流天地！\n\n在这里您可以：\n• 分享您的咖啡体验\n• 查看其他用户的精彩动态\n• 参与话题讨论\n• 了解咖啡文化知识\n• 获取最新活动资讯\n\n点击底部"社区"加入我们的咖啡大家庭吧！最近热门话题有"秋日限定饮品"和"咖啡拉花技巧分享"哦～';
  }

  String _getHotCommunityPosts() {
    final posts = _dataService.getCommunityPosts();
    final hotPosts = posts.where((post) => post.likes > 50).take(3).toList();

    if (hotPosts.isEmpty) {
      return '社区最近比较安静呢～不过您可以点击底部"社区"查看所有动态，或者分享您的咖啡体验！';
    }

    String response = '🔥 最近社区热点动态：\n\n';

    for (int i = 0; i < hotPosts.length; i++) {
      final post = hotPosts[i];
      final shortContent = post.content.length > 30
          ? '${post.content.substring(0, 30)}...'
          : post.content;
      response += '${i + 1}. $shortContent\n';
      response += '👤 用户${post.userId} • ❤️ ${post.likes} • 💬 ${post.comments}\n';
      response += '👆 [点击查看详情](post:${post.id})\n\n';
    }

    response += '点击上方链接查看完整动态，或前往社区页面参与讨论！';
    return response;
  }

  String _getOrderInfo(String input) {
    if (input.contains('查询')) {
      return '您可以通过以下方式查询订单：\n\n1. 点击"我的"-"我的订单"查看所有订单\n2. 在订单页面可以看到订单状态\n3. 订单状态包括：待付款、制作中、待取餐、已完成\n\n如需帮助，请联系客服：400-123-4567';
    }
    
    return '📋 关于订单服务：\n\n• 支持在线下单，到店取餐\n• 部分区域支持外卖配送\n• 订单制作时间约10-15分钟\n• 支持微信、支付宝等多种支付方式\n• 可预约取餐时间\n\n您可以在菜单页面选择商品下单，或告诉我您想要什么，我来帮您下单！';
  }

  String _getGreeting() {
    final greetings = [
      '你好！很高兴为您服务！我是小盛，您的专属咖啡助手。有什么我可以帮助您的吗？',
      '嗨！欢迎来到新易盛咖啡馆！我可以为您介绍菜单、推荐饮品、查询活动等。请告诉我您需要什么帮助～',
      '您好！今天想来点什么呢？我可以为您推荐人气饮品，或者帮您了解我们的优惠活动！',
    ];
    return greetings[Random().nextInt(greetings.length)];
  }

  String _getThanksResponse() {
    final responses = [
      '不客气！很高兴能帮助到您。还有其他问题随时问我哦～',
      '您太客气了！为您服务是我的荣幸。祝您用餐愉快！',
      '不用谢！如果还有其他需要帮助的地方，随时找我聊天吧！',
    ];
    return responses[Random().nextInt(responses.length)];
  }

  String _getDefaultResponse() {
    final responses = [
      '我是小盛，新易盛咖啡馆的AI助手！我可以帮您：\n\n• 了解企业文化\n• 浏览菜单和推荐饮品\n• 查询优惠活动\n• 了解社区动态\n• 协助下单服务\n\n请告诉我您想了解什么？',
      '很抱歉，我没有完全理解您的问题。我可以为您提供以下服务：\n\n☕ 菜单推荐和价格查询\n🎉 优惠活动和会员福利\n📱 下单和订单查询\n💬 社区动态和热点话题\n🏢 企业信息和文化介绍\n\n请选择您感兴趣的话题！',
      '让我来帮助您！作为咖啡助手，我最擅长：\n\n• 根据您的喜好推荐饮品\n• 介绍最新优惠活动\n• 帮助您了解订单状态\n• 分享咖啡文化知识\n\n您想聊聊什么呢？',
    ];
    return responses[Random().nextInt(responses.length)];
  }
}
