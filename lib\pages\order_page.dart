import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../services/mock_data_service.dart';
import '../models/order.dart';
import '../utils/date_utils.dart' as app_date;
import '../utils/app_utils.dart';
import '../utils/page_transitions.dart';
import 'order_detail_page.dart';
import 'comment_page.dart';

/// 订单页面
class OrderPage extends StatefulWidget {
  const OrderPage({super.key});

  @override
  State<OrderPage> createState() => _OrderPageState();
}

class _OrderPageState extends State<OrderPage> with TickerProviderStateMixin {
  late TabController _tabController;
  final MockDataService _dataService = MockDataService();

  final List<Map<String, String>> _tabs = [
    {'label': '全部', 'status': 'all'},
    {'label': '待付款', 'status': AppConstants.orderStatusPending},
    {'label': '制作中', 'status': AppConstants.orderStatusPreparing},
    {'label': '待取餐', 'status': AppConstants.orderStatusReady},
    {'label': '已完成', 'status': AppConstants.orderStatusCompleted},
  ];

  List<Order> _allOrders = [];
  Map<String, List<Order>> _ordersByStatus = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _loadOrders();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadOrders() {
    setState(() {
      _allOrders = _dataService.getOrders();
      _ordersByStatus = {
        'all': _allOrders,
        AppConstants.orderStatusPending: _dataService.getOrdersByStatus(AppConstants.orderStatusPending),
        AppConstants.orderStatusPreparing: _dataService.getOrdersByStatus(AppConstants.orderStatusPreparing),
        AppConstants.orderStatusReady: _dataService.getOrdersByStatus(AppConstants.orderStatusReady),
        AppConstants.orderStatusCompleted: _dataService.getOrdersByStatus(AppConstants.orderStatusCompleted),
      };
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text('我的订单'),
        automaticallyImplyLeading: true,
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: _tabs.map((tab) => Tab(text: tab['label'])).toList(),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: _tabs.map((tab) => _buildOrderList(tab['status']!)).toList(),
      ),
    );
  }

  Widget _buildOrderList(String status) {
    final orders = _ordersByStatus[status] ?? [];

    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 64,
              color: AppConstants.textLight,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              '暂无订单',
              style: const TextStyle(
                color: AppConstants.textLight,
                fontSize: AppConstants.fontSizeLarge,
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              '快去点一杯咖啡吧',
              style: const TextStyle(
                color: AppConstants.textLight,
                fontSize: AppConstants.fontSizeMedium,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _loadOrders();
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        itemCount: orders.length,
        itemBuilder: (context, index) {
          final order = orders[index];
          return _buildOrderItem(order);
        },
      ),
    );
  }

  Widget _buildOrderItem(Order order) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          _showOrderDetail(order);
        },
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 订单头部信息
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    order.id,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeMedium,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(order.status).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      order.statusText,
                      style: TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                        color: _getStatusColor(order.status),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.paddingSmall),

              // 订单商品列表
              Column(
                children: order.items.map((item) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            '${item.name} x${item.quantity}',
                            style: const TextStyle(
                              fontSize: AppConstants.fontSizeMedium,
                              color: AppConstants.textPrimary,
                            ),
                          ),
                        ),
                        Text(
                          AppUtils.formatPrice(item.totalPrice),
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeMedium,
                            color: AppConstants.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),

              const Divider(height: AppConstants.paddingMedium),

              // 订单底部信息
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '下单时间：${app_date.AppDateUtils.formatDateTime(order.orderTime)}',
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeSmall,
                            color: AppConstants.textLight,
                          ),
                        ),
                        if (order.estimatedTime != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            '预计完成：${app_date.AppDateUtils.formatDateTime(order.estimatedTime!)}',
                            style: const TextStyle(
                              fontSize: AppConstants.fontSizeSmall,
                              color: AppConstants.primaryColor,
                            ),
                          ),
                        ],
                        const SizedBox(height: 2),
                        Text(
                          '取餐地点：${order.pickupLocation}',
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeSmall,
                            color: AppConstants.textLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '总计',
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: AppConstants.textLight,
                        ),
                      ),
                      Text(
                        AppUtils.formatPrice(order.totalAmount),
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeLarge,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              // 操作按钮
              if (order.canCancel || order.isReady || order.status == AppConstants.orderStatusCompleted) ...[
                const SizedBox(height: AppConstants.paddingMedium),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (order.canCancel) ...[
                      TextButton(
                        onPressed: () {
                          _cancelOrder(order);
                        },
                        child: const Text('取消订单'),
                      ),
                      const SizedBox(width: AppConstants.paddingSmall),
                    ],
                    if (order.isReady) ...[
                      ElevatedButton(
                        onPressed: () {
                          _confirmPickup(order);
                        },
                        child: const Text('确认取餐'),
                      ),
                    ],
                    if (order.status == AppConstants.orderStatusCompleted) ...[
                      ElevatedButton(
                        onPressed: () {
                          _goToComment(order);
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppConstants.primaryColor,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('去评论'),
                      ),
                    ],
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case AppConstants.orderStatusPending:
        return AppConstants.warningColor;
      case AppConstants.orderStatusPreparing:
        return AppConstants.primaryColor;
      case AppConstants.orderStatusReady:
        return AppConstants.successColor;
      case AppConstants.orderStatusCompleted:
        return AppConstants.textSecondary;
      case AppConstants.orderStatusCancelled:
        return AppConstants.errorColor;
      default:
        return AppConstants.textLight;
    }
  }

  void _showOrderDetail(Order order) {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => OrderDetailPage(order: order),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: animation.drive(
              Tween(begin: const Offset(1.0, 0.0), end: Offset.zero)
                  .chain(CurveTween(curve: Curves.easeInOut)),
            ),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }

  void _cancelOrder(Order order) async {
    final confirmed = await AppUtils.showConfirmDialog(
      context,
      title: '取消订单',
      content: '确定要取消这个订单吗？',
    );

    if (confirmed == true) {
      AppUtils.showSuccess(context, '订单已取消');
      _loadOrders();
    }
  }

  void _confirmPickup(Order order) async {
    final confirmed = await AppUtils.showConfirmDialog(
      context,
      title: '确认取餐',
      content: '确认已取到餐品吗？',
    );

    if (confirmed == true) {
      AppUtils.showSuccess(context, '取餐确认成功');
      _loadOrders();
    }
  }

  void _goToComment(Order order) {
    // 导航到评论页面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CommentPage(order: order),
      ),
    );
  }
}
