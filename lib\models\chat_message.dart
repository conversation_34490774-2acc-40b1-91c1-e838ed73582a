import 'package:json_annotation/json_annotation.dart';

part 'chat_message.g.dart';

/// 聊天消息模型
@JsonSerializable()
class ChatMessage {
  /// 消息ID
  final String id;
  
  /// 消息内容
  final String content;
  
  /// 是否为用户消息
  final bool isUser;
  
  /// 时间戳
  final DateTime timestamp;
  
  /// 消息类型（可选）
  final String? type;
  
  /// 附加数据（可选）
  final Map<String, dynamic>? metadata;

  const ChatMessage({
    required this.id,
    required this.content,
    required this.isUser,
    required this.timestamp,
    this.type,
    this.metadata,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) =>
      _$ChatMessageFromJson(json);

  Map<String, dynamic> toJson() => _$ChatMessageToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChatMessage &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ChatMessage{id: $id, content: $content, isUser: $isUser, timestamp: $timestamp}';
  }

  /// 创建用户消息
  factory ChatMessage.user({
    required String content,
    String? type,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      isUser: true,
      timestamp: DateTime.now(),
      type: type,
      metadata: metadata,
    );
  }

  /// 创建AI消息
  factory ChatMessage.ai({
    required String content,
    String? type,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      isUser: false,
      timestamp: DateTime.now(),
      type: type,
      metadata: metadata,
    );
  }

  /// 复制并修改消息
  ChatMessage copyWith({
    String? id,
    String? content,
    bool? isUser,
    DateTime? timestamp,
    String? type,
    Map<String, dynamic>? metadata,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      content: content ?? this.content,
      isUser: isUser ?? this.isUser,
      timestamp: timestamp ?? this.timestamp,
      type: type ?? this.type,
      metadata: metadata ?? this.metadata,
    );
  }
}
