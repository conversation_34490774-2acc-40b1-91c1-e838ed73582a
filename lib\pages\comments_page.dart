import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/community_post.dart';
import '../utils/app_utils.dart';

/// 评论页面
class CommentsPage extends StatefulWidget {
  final CommunityPost post;

  const CommentsPage({
    super.key,
    required this.post,
  });

  @override
  State<CommentsPage> createState() => _CommentsPageState();
}

class _CommentsPageState extends State<CommentsPage> {
  final TextEditingController _commentController = TextEditingController();
  final List<Map<String, dynamic>> _comments = [];
  final Map<int, bool> _likedComments = {};
  final Map<int, int> _commentLikeCounts = {};

  @override
  void initState() {
    super.initState();
    _loadComments();
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  void _loadComments() {
    // 模拟评论数据
    _comments.addAll([
      {
        'id': 1,
        'avatar': 'assets/images/avatar/avatar1.jpg',
        'username': '咖啡爱好者',
        'content': '看起来很棒！我也想试试这个拉花技巧',
        'time': '2小时前',
        'likes': 5,
      },
      {
        'id': 2,
        'avatar': 'assets/images/avatar/avatar2.jpg',
        'username': '小美',
        'content': '太厉害了，请问有教程吗？',
        'time': '1小时前',
        'likes': 3,
      },
      {
        'id': 3,
        'avatar': 'assets/images/avatar/avatar3.jpg',
        'username': '咖啡师小王',
        'content': '专业！这个拉花确实需要很多练习',
        'time': '30分钟前',
        'likes': 8,
      },
      {
        'id': 4,
        'avatar': 'assets/images/avatar/avatar4.jpg',
        'username': '拉花新手',
        'content': '请问用的是什么牛奶？全脂还是脱脂？',
        'time': '15分钟前',
        'likes': 2,
      },
      {
        'id': 5,
        'avatar': 'assets/images/avatar/avatar5.jpg',
        'username': '咖啡达人',
        'content': '温度控制很重要，牛奶温度要在60-65度之间',
        'time': '10分钟前',
        'likes': 12,
      },
    ]);

    // 初始化点赞状态
    for (var comment in _comments) {
      _commentLikeCounts[comment['id']] = comment['likes'];
      _likedComments[comment['id']] = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: Text('评论 (${widget.post.comments + _comments.length})'),
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // 原动态内容预览
          _buildPostPreview(),

          const Divider(height: 1),

          // 评论列表
          Expanded(
            child: _buildCommentsList(),
          ),

          // 评论输入框
          _buildCommentInput(),
        ],
      ),
    );
  }

  Widget _buildPostPreview() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用户信息
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundImage: AssetImage('assets/images/avatar/avatar${(widget.post.userId % 5) + 1}.jpg'),
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '用户${widget.post.userId}',
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeMedium,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.textPrimary,
                      ),
                    ),
                    Text(
                      widget.post.timestamp,
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                        color: AppConstants.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // 动态内容
          Text(
            widget.post.content,
            style: const TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: AppConstants.textPrimary,
              height: 1.4,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),

          // 图片预览（如果有）
          if (widget.post.images.isNotEmpty) ...[
            const SizedBox(height: AppConstants.paddingSmall),
            SizedBox(
              height: 60,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: widget.post.images.length > 3 ? 3 : widget.post.images.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.only(right: AppConstants.paddingSmall),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                      child: Image.asset(
                        _getLocalImagePath(index),
                        width: 60,
                        height: 60,
                        fit: BoxFit.cover,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCommentsList() {
    if (_comments.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: AppConstants.textLight,
            ),
            SizedBox(height: AppConstants.paddingMedium),
            Text(
              '还没有评论',
              style: TextStyle(
                color: AppConstants.textLight,
                fontSize: AppConstants.fontSizeLarge,
              ),
            ),
            SizedBox(height: AppConstants.paddingSmall),
            Text(
              '快来发表第一条评论吧！',
              style: TextStyle(
                color: AppConstants.textSecondary,
                fontSize: AppConstants.fontSizeMedium,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        // 模拟刷新
        await Future.delayed(const Duration(seconds: 1));
        setState(() {
          // 可以在这里重新加载评论
        });
      },
      child: ListView.separated(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        itemCount: _comments.length,
        separatorBuilder: (context, index) => const SizedBox(height: AppConstants.paddingLarge),
        itemBuilder: (context, index) {
          final comment = _comments[index];
          return _buildCommentItem(comment);
        },
      ),
    );
  }

  Widget _buildCommentItem(Map<String, dynamic> comment) {
    final commentId = comment['id'] as int;
    final isLiked = _likedComments[commentId] ?? false;
    final likeCount = _commentLikeCounts[commentId] ?? comment['likes'];

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用户信息
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundImage: AssetImage(comment['avatar']),
              ),
              const SizedBox(width: AppConstants.paddingSmall),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      comment['username'],
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeMedium,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.textPrimary,
                      ),
                    ),
                    Text(
                      comment['time'],
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                        color: AppConstants.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // 评论内容
          Text(
            comment['content'],
            style: const TextStyle(
              fontSize: AppConstants.fontSizeMedium,
              color: AppConstants.textPrimary,
              height: 1.4,
            ),
          ),

          const SizedBox(height: AppConstants.paddingSmall),

          // 互动按钮
          Row(
            children: [
              GestureDetector(
                onTap: () => _toggleCommentLike(commentId),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      isLiked ? Icons.favorite : Icons.favorite_border,
                      size: 16,
                      color: isLiked ? AppConstants.errorColor : AppConstants.textSecondary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      likeCount.toString(),
                      style: TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                        color: isLiked ? AppConstants.errorColor : AppConstants.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: AppConstants.paddingLarge),

              GestureDetector(
                onTap: () => _replyComment(comment['username']),
                child: const Text(
                  '回复',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeSmall,
                    color: AppConstants.textSecondary,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCommentInput() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: AppConstants.borderColor, width: 0.5),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 评论输入框
            Expanded(
              child: TextField(
                controller: _commentController,
                decoration: InputDecoration(
                  hintText: '写评论...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20),
                    borderSide: const BorderSide(color: AppConstants.borderColor),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                ),
                maxLines: null,
                textInputAction: TextInputAction.send,
                onSubmitted: (_) => _sendComment(),
              ),
            ),

            const SizedBox(width: AppConstants.paddingSmall),

            // 发送按钮
            GestureDetector(
              onTap: _sendComment,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: const BoxDecoration(
                  color: AppConstants.primaryColor,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.send,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleCommentLike(int commentId) {
    setState(() {
      final currentLiked = _likedComments[commentId] ?? false;
      final currentCount = _commentLikeCounts[commentId] ?? 0;

      _likedComments[commentId] = !currentLiked;
      _commentLikeCounts[commentId] = currentLiked ? currentCount - 1 : currentCount + 1;
    });
    AppUtils.vibrate();
    AppUtils.showToast(context, _likedComments[commentId]! ? '已点赞' : '已取消点赞');
  }

  void _replyComment(String username) {
    _commentController.text = '@$username ';
    // 聚焦到输入框
    FocusScope.of(context).requestFocus(FocusNode());
  }

  void _sendComment() {
    if (_commentController.text.trim().isEmpty) return;

    setState(() {
      _comments.insert(0, {
        'id': DateTime.now().millisecondsSinceEpoch,
        'avatar': 'assets/images/avatar/avatar1.jpg',
        'username': '我',
        'content': _commentController.text.trim(),
        'time': '刚刚',
        'likes': 0,
      });

      // 初始化新评论的点赞状态
      final newCommentId = _comments[0]['id'] as int;
      _commentLikeCounts[newCommentId] = 0;
      _likedComments[newCommentId] = false;
    });

    _commentController.clear();
    AppUtils.showSuccess(context, '评论发送成功');
  }

  String _getLocalImagePath(int index) {
    // 根据索引返回本地社区图片路径
    final imageIndex = (index % 4) + 1; // 循环使用4张图片
    return 'assets/images/community/community$imageIndex.jpg';
  }
}