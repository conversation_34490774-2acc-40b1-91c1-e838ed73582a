# 新易盛咖啡馆 Flutter 应用

这是新易盛咖啡馆移动端应用的Flutter版本，从原来的uni-app项目完整迁移而来，保持了界面的完整性和功能特性。

## 项目概述

新易盛咖啡馆Flutter应用是一个企业级移动应用，为新易盛员工提供便捷的咖啡馆服务体验。应用集成了在线点餐、社区交流、活动参与等核心功能，特别突出了推特风格的社区功能。

## 核心功能

### 🏠 首页
- 欢迎横幅和品牌展示
- 快捷功能入口（点餐、订单、社区、活动）
- 今日推荐商品展示
- 热门社区动态预览
- 即将到来的活动信息

### ☕ 菜单
- 商品分类浏览（精品咖啡、特色饮品、轻食简餐）
- 实时搜索功能
- 商品详情展示
- 购物车管理
- 一键加入购物车

### 📋 订单
- 多状态订单管理（全部、待付款、制作中、待取餐、已完成）
- 订单状态实时跟踪
- 订单历史查看
- 取消订单和确认取餐功能
- 下拉刷新支持

### 🌟 社区（推特风格）
- 动态发布与浏览
- 图片分享支持
- 点赞、评论、转发功能
- 标签筛选（全部、关注、热门、附近）
- 实时搜索动态内容
- 用户认证标识

### 👤 个人中心
- 用户信息展示和编辑
- 统计数据（动态、订单、关注）
- 功能入口（订单、收藏、优惠券、积分商城）
- 帮助与反馈
- 关于我们

## 技术栈

- **框架**: Flutter 3.x
- **语言**: Dart
- **状态管理**: Provider
- **网络请求**: Dio
- **本地存储**: SharedPreferences
- **图片加载**: CachedNetworkImage
- **UI组件**: Material Design 3
- **路由管理**: GoRouter
- **国际化**: Intl

## 项目结构

```
lib/
├── constants/          # 常量定义
│   ├── app_constants.dart    # 应用常量
│   └── app_theme.dart        # 主题配置
├── models/            # 数据模型
│   ├── user.dart           # 用户模型
│   ├── community_post.dart # 社区动态模型
│   ├── menu.dart          # 菜单模型
│   ├── order.dart         # 订单模型
│   └── activity.dart      # 活动模型
├── services/          # 服务层
│   ├── app_service.dart        # 应用服务
│   └── mock_data_service.dart  # 模拟数据服务
├── pages/             # 页面
│   ├── home_page.dart      # 首页
│   ├── menu_page.dart      # 菜单页
│   ├── order_page.dart     # 订单页
│   ├── community_page.dart # 社区页
│   └── profile_page.dart   # 个人中心页
├── widgets/           # 通用组件
│   └── main_scaffold.dart  # 主脚手架
├── utils/             # 工具类
│   ├── app_utils.dart      # 应用工具
│   └── date_utils.dart     # 日期工具
├── app.dart           # 应用主文件
└── main.dart          # 入口文件
```

## 安装与运行

### 环境要求

- Flutter SDK >= 3.0.0
- Dart SDK >= 3.0.0
- Android Studio / VS Code
- Android SDK (Android开发)
- Xcode (iOS开发，仅macOS)

### 安装依赖

```bash
flutter pub get
```

### 运行应用

#### 调试模式
```bash
flutter run
```

#### 发布模式
```bash
flutter run --release
```

### 构建应用

#### Android APK
```bash
flutter build apk
```

#### Android App Bundle
```bash
flutter build appbundle
```

#### iOS
```bash
flutter build ios
```

## 功能特色

### 界面设计
- 采用Material Design 3设计规范
- 咖啡色主题，符合品牌调性
- 响应式布局，适配不同屏幕尺寸
- 流畅的动画和交互效果

### 用户体验
- 直观的导航结构
- 快速的页面加载
- 离线数据缓存
- 错误处理和用户反馈
- 无障碍访问支持

### 数据管理
- 模块化的数据模型
- 统一的状态管理
- 本地数据持久化
- 模拟数据服务

## 开发说明

### 代码规范
- 遵循Dart官方代码规范
- 使用flutter_lints进行代码检查
- 组件化开发，提高代码复用性
- 完善的注释和文档

### 测试
```bash
# 运行单元测试
flutter test

# 运行集成测试
flutter drive --target=test_driver/app.dart
```

### 代码生成
```bash
# 生成JSON序列化代码
flutter packages pub run build_runner build
```

## 部署

### Android
1. 配置签名密钥
2. 构建发布版本
3. 上传到Google Play Store

### iOS
1. 配置开发者证书
2. 构建发布版本
3. 上传到App Store Connect

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License

## 联系我们

- 项目地址：https://github.com/xys-coffee/flutter-app
- 问题反馈：https://github.com/xys-coffee/flutter-app/issues
- 邮箱：<EMAIL>

---

**注意**: 这是从uni-app项目迁移到Flutter的完整版本，保持了原有的界面设计和功能特性，同时利用Flutter的优势提供了更好的性能和用户体验。
