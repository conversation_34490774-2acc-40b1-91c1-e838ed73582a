import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'constants/app_constants.dart';
import 'constants/app_theme.dart';
import 'services/app_service.dart';
import 'widgets/main_scaffold.dart';

/// 新易盛咖啡馆主应用
class XYSCoffeeApp extends StatelessWidget {
  const XYSCoffeeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppService>(
      builder: (context, appService, child) {
        // 如果服务未初始化，显示加载页面
        if (!appService.isInitialized) {
          return MaterialApp(
            title: AppConstants.appName,
            theme: AppTheme.lightTheme,
            home: const _LoadingPage(),
            debugShowCheckedModeBanner: false,
          );
        }
        
        return MaterialApp(
          title: AppConstants.appName,
          theme: AppTheme.lightTheme,
          home: const MainScaffold(),
          debugShowCheckedModeBanner: false,
          // 全局错误处理
          builder: (context, widget) {
            // 处理文本缩放
            return MediaQuery(
              data: MediaQuery.of(context).copyWith(
                textScaler: TextScaler.linear(
                  MediaQuery.of(context).textScaler.scale(1.0).clamp(0.8, 1.2),
                ),
              ),
              child: widget!,
            );
          },
        );
      },
    );
  }
}

/// 加载页面
class _LoadingPage extends StatelessWidget {
  const _LoadingPage();

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 应用图标
            Icon(
              Icons.local_cafe,
              size: 80,
              color: AppConstants.primaryColor,
            ),
            SizedBox(height: AppConstants.paddingLarge),
            
            // 应用名称
            Text(
              AppConstants.appName,
              style: TextStyle(
                fontSize: AppConstants.fontSizeXXLarge,
                fontWeight: FontWeight.bold,
                color: AppConstants.textPrimary,
              ),
            ),
            SizedBox(height: AppConstants.paddingSmall),
            
            // 副标题
            Text(
              '连接全球，品味生活',
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                color: AppConstants.textSecondary,
              ),
            ),
            SizedBox(height: AppConstants.paddingXLarge),
            
            // 加载指示器
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppConstants.primaryColor),
            ),
          ],
        ),
      ),
    );
  }
}
