import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../services/mock_data_service.dart';
import '../models/menu.dart';
import '../utils/app_utils.dart';
import '../utils/page_transitions.dart';
import '../widgets/product_spec_modal.dart';
import 'product_detail_page.dart';
import 'cart_page.dart';

/// 菜单页面
class MenuPage extends StatefulWidget {
  const MenuPage({super.key});

  @override
  State<MenuPage> createState() => _MenuPageState();
}

class _MenuPageState extends State<MenuPage> with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  final MockDataService _dataService = MockDataService();
  late TabController _tabController;

  List<MenuCategory> _categories = [];
  List<MenuItem> _filteredItems = [];
  String _searchKeyword = '';
  int _selectedCategoryIndex = 0;
  int _cartItemCount = 0;
  Map<String, int> _itemQuantities = {}; // 商品数量管理

  // 筛选相关状态
  String _priceFilter = 'all'; // all, low, medium, high
  String _temperatureFilter = 'all'; // all, hot, iced, both
  bool _popularOnly = false;
  bool _discountOnly = false;

  // 排序相关状态
  String _sortBy = 'default'; // default, price_asc, price_desc, popularity, rating

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _loadData() {
    setState(() {
      _categories = _dataService.getMenuCategories();
      if (_categories.isNotEmpty) {
        _tabController = TabController(length: _categories.length, vsync: this);
        _filteredItems = _categories[0].items;
      }
    });
  }

  void _onSearch(String keyword) {
    setState(() {
      _searchKeyword = keyword;
      if (keyword.isEmpty) {
        _filteredItems = _categories[_selectedCategoryIndex].items;
      } else {
        _filteredItems = _categories[_selectedCategoryIndex]
            .items
            .where((item) =>
                item.name.toLowerCase().contains(keyword.toLowerCase()) ||
                item.description.toLowerCase().contains(keyword.toLowerCase()))
            .toList();
      }
    });
  }

  void _onCategoryChanged(int index) {
    setState(() {
      _selectedCategoryIndex = index;
      _searchKeyword = '';
      _searchController.clear();
      _filteredItems = _categories[index].items;
    });
  }

  void _filterItems() {
    List<MenuItem> items = _categories[_selectedCategoryIndex].items;

    // 搜索关键词筛选
    if (_searchKeyword.isNotEmpty) {
      items = items
          .where((item) =>
              item.name.toLowerCase().contains(_searchKeyword.toLowerCase()) ||
              item.description.toLowerCase().contains(_searchKeyword.toLowerCase()))
          .toList();
    }

    // 价格筛选
    if (_priceFilter != 'all') {
      items = items.where((item) {
        switch (_priceFilter) {
          case 'low':
            return item.price <= 25;
          case 'medium':
            return item.price > 25 && item.price <= 35;
          case 'high':
            return item.price > 35;
          default:
            return true;
        }
      }).toList();
    }

    // 温度筛选
    if (_temperatureFilter != 'all') {
      items = items.where((item) {
        switch (_temperatureFilter) {
          case 'hot':
            return item.hot == true;
          case 'iced':
            return item.iced == true;
          case 'both':
            return item.hot == true && item.iced == true;
          default:
            return true;
        }
      }).toList();
    }

    // 人气商品筛选
    if (_popularOnly) {
      items = items.where((item) => item.popular).toList();
    }

    // 折扣商品筛选
    if (_discountOnly) {
      items = items.where((item) => item.hasDiscount).toList();
    }

    // 排序
    _sortItems(items);

    _filteredItems = items;
  }

  void _sortItems(List<MenuItem> items) {
    switch (_sortBy) {
      case 'price_asc':
        items.sort((a, b) => a.price.compareTo(b.price));
        break;
      case 'price_desc':
        items.sort((a, b) => b.price.compareTo(a.price));
        break;
      case 'popularity':
        items.sort((a, b) {
          // 人气商品排在前面，然后按ID排序（模拟销量）
          if (a.popular && !b.popular) return -1;
          if (!a.popular && b.popular) return 1;
          return a.id.compareTo(b.id);
        });
        break;
      case 'discount':
        items.sort((a, b) {
          // 有折扣的商品排在前面，然后按折扣幅度排序
          if (a.hasDiscount && !b.hasDiscount) return -1;
          if (!a.hasDiscount && b.hasDiscount) return 1;
          if (a.hasDiscount && b.hasDiscount) {
            return b.discountPercentage.compareTo(a.discountPercentage);
          }
          return 0;
        });
        break;
      case 'default':
      default:
        // 保持原有顺序，不排序
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_categories.isEmpty) {
      return Scaffold(
        backgroundColor: AppConstants.backgroundColor,
        appBar: AppBar(
          title: const Text('菜单'),
          automaticallyImplyLeading: false,
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text('菜单'),
        automaticallyImplyLeading: false,
        actions: [
          // 购物车按钮
          Stack(
            children: [
              IconButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const CartPage()),
                  );
                },
                icon: const Icon(Icons.shopping_cart),
                tooltip: '购物车',
              ),
              if (_cartItemCount > 0)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      '$_cartItemCount',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          // 搜索筛选栏
          _buildSearchFilterBar(),

          // 主要内容区域
          Expanded(
            child: Row(
              children: [
                // 左侧分类栏
                _buildCategorySidebar(),

                // 右侧商品列表
                Expanded(
                  child: _buildMenuContent(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        children: [
          // 搜索框
          Expanded(
            child: Container(
              height: 40,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  hintText: '搜索咖啡、茶饮、小食...',
                  hintStyle: TextStyle(
                    color: AppConstants.textLight,
                    fontSize: 14,
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    color: AppConstants.textLight,
                    size: 20,
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                ),
                style: const TextStyle(fontSize: 14),
                onChanged: _onSearch,
              ),
            ),
          ),

          const SizedBox(width: 12),

          // 筛选按钮
          GestureDetector(
            onTap: _showFilterDialog,
            child: Container(
              height: 40,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: _hasActiveFilters() ? AppConstants.primaryColor : Colors.grey[100],
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: _hasActiveFilters() ? AppConstants.primaryColor : Colors.grey[300]!,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.filter_list,
                    size: 18,
                    color: _hasActiveFilters() ? Colors.white : AppConstants.textSecondary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '筛选',
                    style: TextStyle(
                      fontSize: 14,
                      color: _hasActiveFilters() ? Colors.white : AppConstants.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (_hasActiveFilters()) ...[
                    const SizedBox(width: 4),
                    Container(
                      width: 6,
                      height: 6,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(width: 12),

          // 排序按钮
          GestureDetector(
            onTap: _showSortDialog,
            child: Container(
              height: 40,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: const Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.sort,
                    size: 18,
                    color: AppConstants.textSecondary,
                  ),
                  SizedBox(width: 4),
                  Text(
                    '排序',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppConstants.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuContent() {
    if (_filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: AppConstants.textLight,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              _searchKeyword.isEmpty ? '暂无商品' : '未找到相关商品',
              style: const TextStyle(
                color: AppConstants.textLight,
                fontSize: AppConstants.fontSizeLarge,
              ),
            ),
            if (_searchKeyword.isNotEmpty) ...[
              const SizedBox(height: AppConstants.paddingSmall),
              Text(
                '试试搜索其他关键词',
                style: const TextStyle(
                  color: AppConstants.textLight,
                  fontSize: AppConstants.fontSizeMedium,
                ),
              ),
            ],
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingSmall),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 1, // 改为1列布局，每行显示1个商品
        crossAxisSpacing: AppConstants.paddingSmall,
        mainAxisSpacing: AppConstants.paddingSmall,
        childAspectRatio: 2.2, // 调整比例适合单行布局，增加高度
      ),
      itemCount: _filteredItems.length,
      itemBuilder: (context, index) {
        final item = _filteredItems[index];
        return _buildMenuItem(item);
      },
    );
  }

  Widget _buildMenuItem(MenuItem item) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          _showProductDetail(item);
        },
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        child: Row(
          children: [
            // 商品图片
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppConstants.radiusMedium),
                bottomLeft: Radius.circular(AppConstants.radiusMedium),
              ),
              child: Image.asset(
                item.image,
                width: 120,
                height: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  width: 120,
                  height: double.infinity,
                  color: AppConstants.backgroundColor,
                  child: const Icon(
                    Icons.local_cafe,
                    color: AppConstants.textLight,
                    size: 40,
                  ),
                ),
              ),
            ),

            // 商品信息
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.paddingSmall),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // 商品名称和热销标签
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            item.name,
                            style: const TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: AppConstants.textPrimary,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (item.popular)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 4,
                              vertical: 1,
                            ),
                            decoration: BoxDecoration(
                              color: AppConstants.primaryColor,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              '热销',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 9,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),

                    // 商品描述
                    Text(
                      item.description,
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppConstants.textSecondary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    // 价格和数量选择
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          AppUtils.formatPrice(item.price),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppConstants.primaryColor,
                          ),
                        ),
                        _buildQuantitySelector(item),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showProductDetail(MenuItem item) {
    context.pushSlideFromRight(ProductDetailPage(product: item));
  }

  Widget _buildQuantitySelector(MenuItem item) {
    // 显示添加按钮
    return Container(
      width: 26,
      height: 26,
      decoration: BoxDecoration(
        color: AppConstants.primaryColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: IconButton(
        onPressed: () => _addToCart(item),
        icon: const Icon(
          Icons.add,
          color: Colors.white,
          size: 14,
        ),
        padding: EdgeInsets.zero,
      ),
    );
  }

  void _addToCart(MenuItem item) {
    _showProductSpecModal(item);
  }

  void _showProductSpecModal(MenuItem item) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ProductSpecModal(
        product: item,
        onAddToCart: (product, quantity, specs) {
          setState(() {
            final currentQuantity = _itemQuantities[item.id.toString()] ?? 0;
            _itemQuantities[item.id.toString()] = currentQuantity + quantity;
            _cartItemCount += quantity;
          });
          AppUtils.showSuccess(context, '${item.name} x$quantity 已加入购物车');
          AppUtils.vibrate();
        },
      ),
    );
  }

  void _decreaseQuantity(MenuItem item) {
    setState(() {
      final currentQuantity = _itemQuantities[item.id.toString()] ?? 0;
      if (currentQuantity > 0) {
        _itemQuantities[item.id.toString()] = currentQuantity - 1;
        _cartItemCount--;
        if (_itemQuantities[item.id.toString()] == 0) {
          _itemQuantities.remove(item.id.toString());
        }
      }
    });
  }

  Widget _buildCategorySidebar() {
    return Container(
      width: 80,
      color: Colors.grey[50],
      child: ListView.builder(
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = index == _selectedCategoryIndex;

          return GestureDetector(
            onTap: () => _onCategoryChanged(index),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
              decoration: BoxDecoration(
                color: isSelected ? Colors.white : Colors.transparent,
                border: Border(
                  right: BorderSide(
                    color: isSelected
                        ? AppConstants.primaryColor
                        : Colors.transparent,
                    width: 3,
                  ),
                ),
              ),
              child: Column(
                children: [
                  Text(
                    category.icon,
                    style: const TextStyle(fontSize: 20),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    category.name,
                    style: TextStyle(
                      fontSize: 12,
                      color: isSelected
                          ? AppConstants.primaryColor
                          : AppConstants.textSecondary,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }



  bool _hasActiveFilters() {
    return _priceFilter != 'all' ||
           _temperatureFilter != 'all' ||
           _popularOnly ||
           _discountOnly;
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('排序方式'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildSortOption('默认排序', 'default'),
            _buildSortOption('价格从低到高', 'price_asc'),
            _buildSortOption('价格从高到低', 'price_desc'),
            _buildSortOption('人气优先', 'popularity'),
            _buildSortOption('折扣优先', 'discount'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  Widget _buildSortOption(String title, String value) {
    return RadioListTile<String>(
      title: Text(title),
      value: value,
      groupValue: _sortBy,
      onChanged: (String? newValue) {
        if (newValue != null) {
          setState(() {
            _sortBy = newValue;
            _filterItems();
          });
          Navigator.of(context).pop();
        }
      },
      dense: true,
      contentPadding: EdgeInsets.zero,
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('商品筛选'),
        content: StatefulBuilder(
          builder: (context, setDialogState) {
            return SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 价格筛选
                  const Text(
                    '价格区间',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: [
                      _buildFilterChip(
                        label: '全部',
                        isSelected: _priceFilter == 'all',
                        onTap: () => setDialogState(() => _priceFilter = 'all'),
                      ),
                      _buildFilterChip(
                        label: '25元以下',
                        isSelected: _priceFilter == 'low',
                        onTap: () => setDialogState(() => _priceFilter = 'low'),
                      ),
                      _buildFilterChip(
                        label: '25-35元',
                        isSelected: _priceFilter == 'medium',
                        onTap: () => setDialogState(() => _priceFilter = 'medium'),
                      ),
                      _buildFilterChip(
                        label: '35元以上',
                        isSelected: _priceFilter == 'high',
                        onTap: () => setDialogState(() => _priceFilter = 'high'),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // 温度筛选
                  const Text(
                    '温度选择',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: [
                      _buildFilterChip(
                        label: '全部',
                        isSelected: _temperatureFilter == 'all',
                        onTap: () => setDialogState(() => _temperatureFilter = 'all'),
                      ),
                      _buildFilterChip(
                        label: '仅热饮',
                        isSelected: _temperatureFilter == 'hot',
                        onTap: () => setDialogState(() => _temperatureFilter = 'hot'),
                      ),
                      _buildFilterChip(
                        label: '仅冷饮',
                        isSelected: _temperatureFilter == 'iced',
                        onTap: () => setDialogState(() => _temperatureFilter = 'iced'),
                      ),
                      _buildFilterChip(
                        label: '冷热皆可',
                        isSelected: _temperatureFilter == 'both',
                        onTap: () => setDialogState(() => _temperatureFilter = 'both'),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // 其他筛选
                  const Text(
                    '其他筛选',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  CheckboxListTile(
                    title: const Text('仅显示人气商品'),
                    value: _popularOnly,
                    onChanged: (value) => setDialogState(() => _popularOnly = value ?? false),
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),
                  CheckboxListTile(
                    title: const Text('仅显示折扣商品'),
                    value: _discountOnly,
                    onChanged: (value) => setDialogState(() => _discountOnly = value ?? false),
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
              ),
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              // 重置筛选
              setState(() {
                _priceFilter = 'all';
                _temperatureFilter = 'all';
                _popularOnly = false;
                _discountOnly = false;
                _filterItems();
              });
              Navigator.of(context).pop();
            },
            child: const Text('重置'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _filterItems();
              });
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? AppConstants.primaryColor : Colors.grey[200],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? AppConstants.primaryColor : Colors.grey[300]!,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black87,
            fontSize: 12,
          ),
        ),
      ),
    );
  }

}
