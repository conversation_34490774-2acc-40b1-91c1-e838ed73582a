import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../services/mock_data_service.dart';
import '../services/app_service.dart';
import '../models/user.dart';
import '../utils/app_utils.dart';
import 'order_page.dart';

/// 个人中心页面
class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final MockDataService _dataService = MockDataService();
  late User _currentUser;
  Map<String, int> _userStats = {};

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  void _loadUserData() {
    setState(() {
      _currentUser = _dataService.currentUser;
      _userStats = {
        'posts': _dataService
            .getCommunityPosts()
            .where((post) => post.userId == _currentUser.id)
            .length,
        'orders': _dataService.getOrders().length,
        'followers': 156,
        'following': 89,
      };
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text('我的'),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed: () {
              // TODO: 消息页面
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('消息功能开发中...')),
              );
            },
            icon: const Icon(Icons.message_outlined),
            tooltip: '消息',
          ),
          IconButton(
            onPressed: () {
              // TODO: 设置页面
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('设置功能开发中...')),
              );
            },
            icon: const Icon(Icons.settings),
            tooltip: '设置',
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 用户信息卡片
            _buildUserCard(),

            const SizedBox(height: AppConstants.paddingMedium),

            // 功能菜单
            _buildMenuSection(),

            // 底部边距，避免被导航栏遮挡
            const SizedBox(height: 100),
          ],
        ),
      ),
    );
  }

  Widget _buildUserCard() {
    return Container(
      margin: const EdgeInsets.all(AppConstants.paddingLarge),
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 头像
          Stack(
            children: [
              CircleAvatar(
                radius: 40,
                backgroundImage: AssetImage(_currentUser.avatar),
                onBackgroundImageError: (exception, stackTrace) {},
                child: _currentUser.avatar.isEmpty
                    ? const Icon(
                        Icons.person,
                        size: 40,
                        color: AppConstants.primaryColor,
                      )
                    : null,
              ),
              Positioned(
                right: 0,
                bottom: 0,
                child: GestureDetector(
                  onTap: _editAvatar,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: const BoxDecoration(
                      color: AppConstants.primaryColor,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      size: 16,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(width: AppConstants.paddingLarge),

          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        _currentUser.name,
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeXLarge,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.textPrimary,
                        ),
                      ),
                    ),
                    if (_currentUser.verified) ...[
                      const Icon(
                        Icons.verified,
                        size: 20,
                        color: AppConstants.primaryColor,
                      ),
                      // 编辑按钮
                      IconButton(
                        onPressed: _editProfile,
                        icon: const Icon(Icons.edit),
                        iconSize: 20,
                        color: AppConstants.primaryColor,
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  '${_currentUser.location}',
                  style: const TextStyle(
                    fontSize: AppConstants.fontSizeMedium,
                    color: AppConstants.textSecondary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '工号：${_currentUser.employeeId ?? 'N/A'}',
                  style: const TextStyle(
                    fontSize: AppConstants.fontSizeSmall,
                    color: AppConstants.textLight,
                  ),
                ),
                if (_currentUser.level != null) ...[
                  const SizedBox(height: 4),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: AppConstants.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${_currentUser.level}会员',
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                        color: AppConstants.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
                const SizedBox(height: AppConstants.paddingSmall),

                // 统计信息
                Row(
                  children: [
                    _buildStatItem(
                        '动态', _userStats['posts']?.toString() ?? '0'),
                    const SizedBox(width: AppConstants.paddingLarge),
                    _buildStatItem(
                        '粉丝', _userStats['followers']?.toString() ?? '0'),
                    const SizedBox(width: AppConstants.paddingLarge),
                    _buildStatItem(
                        '关注', _userStats['following']?.toString() ?? '0'),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: AppConstants.fontSizeLarge,
            fontWeight: FontWeight.bold,
            color: AppConstants.primaryColor,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: AppConstants.fontSizeSmall,
            color: AppConstants.textLight,
          ),
        ),
      ],
    );
  }

  Widget _buildMenuSection() {
    final menuItems = [
      {'icon': Icons.receipt_long, 'title': '我的订单', 'subtitle': '查看订单历史'},
      {'icon': Icons.stars, 'title': '我的活动', 'subtitle': '兑换精美礼品'},
      {'icon': Icons.favorite, 'title': '我的收藏', 'subtitle': '收藏的商品和动态'},
      {'icon': Icons.card_giftcard, 'title': '优惠券', 'subtitle': '3张可用'},
      {'icon': Icons.help_outline, 'title': '帮助与反馈', 'subtitle': '常见问题和意见反馈'},
      {'icon': Icons.info_outline, 'title': '关于我们', 'subtitle': '了解新易盛咖啡馆'},
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
      ),
      child: Column(
        children: menuItems.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final isLast = index == menuItems.length - 1;

          return Column(
            children: [
              ListTile(
                leading: Icon(
                  item['icon'] as IconData,
                  color: AppConstants.primaryColor,
                ),
                title: Text(
                  item['title'] as String,
                  style: const TextStyle(
                    fontSize: AppConstants.fontSizeMedium,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                subtitle: Text(
                  item['subtitle'] as String,
                  style: const TextStyle(
                    fontSize: AppConstants.fontSizeSmall,
                    color: AppConstants.textLight,
                  ),
                ),
                trailing: const Icon(
                  Icons.chevron_right,
                  color: AppConstants.textLight,
                ),
                onTap: () => _handleMenuTap(item['title'] as String),
              ),
              if (!isLast)
                const Divider(
                  height: 1,
                  indent: 56,
                  endIndent: 16,
                ),
            ],
          );
        }).toList(),
      ),
    );
  }

  void _editAvatar() {
    AppUtils.showToast(context, '头像编辑功能开发中...');
  }

  void _editProfile() {
    AppUtils.showToast(context, '个人信息编辑功能开发中...');
  }

  void _handleMenuTap(String title) {
    switch (title) {
      case '我的订单':
        // 导航到订单页面 - 右到左滑出效果
        Navigator.of(context).push(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => const OrderPage(),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              const begin = Offset(1.0, 0.0);
              const end = Offset.zero;
              const curve = Curves.ease;
              var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
              return SlideTransition(
                position: animation.drive(tween),
                child: child,
              );
            },
          ),
        );
        break;
      case '我的收藏':
        AppUtils.showToast(context, '收藏功能开发中...');
        break;
      case '优惠券':
        _showCoupons();
        break;
      case '积分商城':
        AppUtils.showToast(context, '积分商城功能开发中...');
        break;
      case '帮助与反馈':
        _showHelpAndFeedback();
        break;
      case '关于我们':
        _showAbout();
        break;
      default:
        AppUtils.showToast(context, '功能开发中...');
    }
  }

  void _showCoupons() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('我的优惠券'),
        content: const Text('您有3张可用优惠券'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showHelpAndFeedback() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('帮助与反馈'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('常见问题'),
            SizedBox(height: 8),
            Text('• 如何下单？'),
            Text('• 如何取消订单？'),
            Text('• 如何联系客服？'),
            SizedBox(height: 16),
            Text('意见反馈'),
            SizedBox(height: 8),
            Text('如有任何问题或建议，请联系我们。'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showAbout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('关于新易盛咖啡馆'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('新易盛咖啡馆'),
            SizedBox(height: 8),
            Text('版本：1.0.0'),
            SizedBox(height: 8),
            Text('连接全球，品味生活'),
            SizedBox(height: 16),
            Text('一个专为新易盛员工打造的企业咖啡馆应用，提供便捷的点餐服务和社区交流平台。'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
