import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../utils/app_utils.dart';

/// 设置页面
class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  bool _pushNotifications = true;
  bool _orderNotifications = true;
  bool _activityNotifications = false;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  bool _darkMode = false;
  String _language = '中文';
  String _cacheSize = '128 MB';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text('设置'),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppConstants.textPrimary),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 通知设置
            _buildNotificationSection(),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // 显示设置
            _buildDisplaySection(),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // 隐私设置
            _buildPrivacySection(),
            
            const SizedBox(height: AppConstants.paddingMedium),
            
            // 其他设置
            _buildOtherSection(),
            
            const SizedBox(height: 100), // 底部留白
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSection() {
    return Container(
      margin: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(AppConstants.paddingLarge),
            child: Text(
              '通知设置',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
                color: AppConstants.textPrimary,
              ),
            ),
          ),
          
          _buildSwitchTile(
            icon: Icons.notifications,
            title: '推送通知',
            subtitle: '接收应用推送消息',
            value: _pushNotifications,
            onChanged: (value) {
              setState(() {
                _pushNotifications = value;
              });
            },
          ),
          
          const Divider(height: 1, indent: 56),
          
          _buildSwitchTile(
            icon: Icons.shopping_cart,
            title: '订单通知',
            subtitle: '订单状态变更提醒',
            value: _orderNotifications,
            onChanged: (value) {
              setState(() {
                _orderNotifications = value;
              });
            },
          ),
          
          const Divider(height: 1, indent: 56),
          
          _buildSwitchTile(
            icon: Icons.event,
            title: '活动通知',
            subtitle: '新活动和优惠信息',
            value: _activityNotifications,
            onChanged: (value) {
              setState(() {
                _activityNotifications = value;
              });
            },
          ),
          
          const Divider(height: 1, indent: 56),
          
          _buildSwitchTile(
            icon: Icons.volume_up,
            title: '声音提醒',
            subtitle: '通知声音开关',
            value: _soundEnabled,
            onChanged: (value) {
              setState(() {
                _soundEnabled = value;
              });
            },
          ),
          
          const Divider(height: 1, indent: 56),
          
          _buildSwitchTile(
            icon: Icons.vibration,
            title: '震动提醒',
            subtitle: '通知震动开关',
            value: _vibrationEnabled,
            onChanged: (value) {
              setState(() {
                _vibrationEnabled = value;
              });
            },
            isLast: true,
          ),
        ],
      ),
    );
  }

  Widget _buildDisplaySection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(AppConstants.paddingLarge),
            child: Text(
              '显示设置',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
                color: AppConstants.textPrimary,
              ),
            ),
          ),
          
          _buildSwitchTile(
            icon: Icons.dark_mode,
            title: '深色模式',
            subtitle: '开启深色主题',
            value: _darkMode,
            onChanged: (value) {
              setState(() {
                _darkMode = value;
              });
              AppUtils.showToast(context, '深色模式功能开发中...');
            },
          ),
          
          const Divider(height: 1, indent: 56),
          
          _buildListTile(
            icon: Icons.language,
            title: '语言设置',
            subtitle: _language,
            onTap: _showLanguageDialog,
            isLast: true,
          ),
        ],
      ),
    );
  }

  Widget _buildPrivacySection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(AppConstants.paddingLarge),
            child: Text(
              '隐私设置',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
                color: AppConstants.textPrimary,
              ),
            ),
          ),
          
          _buildListTile(
            icon: Icons.security,
            title: '账号安全',
            subtitle: '密码、验证等安全设置',
            onTap: () => AppUtils.showToast(context, '账号安全功能开发中...'),
          ),
          
          const Divider(height: 1, indent: 56),
          
          _buildListTile(
            icon: Icons.privacy_tip,
            title: '隐私政策',
            subtitle: '查看隐私保护政策',
            onTap: _showPrivacyPolicy,
          ),
          
          const Divider(height: 1, indent: 56),
          
          _buildListTile(
            icon: Icons.description,
            title: '用户协议',
            subtitle: '查看服务条款',
            onTap: _showUserAgreement,
            isLast: true,
          ),
        ],
      ),
    );
  }

  Widget _buildOtherSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(AppConstants.paddingLarge),
            child: Text(
              '其他设置',
              style: TextStyle(
                fontSize: AppConstants.fontSizeLarge,
                fontWeight: FontWeight.bold,
                color: AppConstants.textPrimary,
              ),
            ),
          ),
          
          _buildListTile(
            icon: Icons.storage,
            title: '清理缓存',
            subtitle: '当前缓存：$_cacheSize',
            onTap: _clearCache,
          ),
          
          const Divider(height: 1, indent: 56),
          
          _buildListTile(
            icon: Icons.system_update,
            title: '检查更新',
            subtitle: '当前版本：1.0.0',
            onTap: _checkUpdate,
          ),
          
          const Divider(height: 1, indent: 56),
          
          _buildListTile(
            icon: Icons.feedback,
            title: '意见反馈',
            subtitle: '帮助我们改进产品',
            onTap: () => AppUtils.showToast(context, '意见反馈功能开发中...'),
          ),
          
          const Divider(height: 1, indent: 56),
          
          _buildListTile(
            icon: Icons.info,
            title: '关于应用',
            subtitle: '版本信息和开发团队',
            onTap: _showAbout,
            isLast: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    bool isLast = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppConstants.primaryColor,
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: AppConstants.fontSizeMedium,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          fontSize: AppConstants.fontSizeSmall,
          color: AppConstants.textLight,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppConstants.primaryColor,
      ),
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingLarge,
        vertical: 4,
      ),
    );
  }

  Widget _buildListTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isLast = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppConstants.primaryColor,
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: AppConstants.fontSizeMedium,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          fontSize: AppConstants.fontSizeSmall,
          color: AppConstants.textLight,
        ),
      ),
      trailing: const Icon(
        Icons.chevron_right,
        color: AppConstants.textLight,
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingLarge,
        vertical: 4,
      ),
    );
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择语言'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('中文'),
              value: '中文',
              groupValue: _language,
              onChanged: (value) {
                setState(() {
                  _language = value!;
                });
                Navigator.of(context).pop();
              },
            ),
            RadioListTile<String>(
              title: const Text('English'),
              value: 'English',
              groupValue: _language,
              onChanged: (value) {
                setState(() {
                  _language = value!;
                });
                Navigator.of(context).pop();
                AppUtils.showToast(context, '多语言功能开发中...');
              },
            ),
          ],
        ),
      ),
    );
  }

  void _clearCache() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清理缓存'),
        content: const Text('确定要清理应用缓存吗？这将删除临时文件和图片缓存。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _cacheSize = '0 MB';
              });
              AppUtils.showSuccess(context, '缓存清理完成');
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _checkUpdate() {
    AppUtils.showToast(context, '正在检查更新...');
    // 模拟检查更新
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        AppUtils.showToast(context, '当前已是最新版本');
      }
    });
  }

  void _showPrivacyPolicy() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('隐私政策'),
        content: const SingleChildScrollView(
          child: Text(
            '新易盛咖啡馆隐私政策\n\n'
            '我们重视您的隐私保护，本政策说明我们如何收集、使用和保护您的个人信息。\n\n'
            '1. 信息收集\n'
            '我们可能收集您的姓名、联系方式、订单信息等。\n\n'
            '2. 信息使用\n'
            '我们使用您的信息来提供服务、处理订单、改进产品。\n\n'
            '3. 信息保护\n'
            '我们采用安全措施保护您的个人信息不被未授权访问。\n\n'
            '如有疑问，请联系我们的客服团队。',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('我知道了'),
          ),
        ],
      ),
    );
  }

  void _showUserAgreement() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('用户协议'),
        content: const SingleChildScrollView(
          child: Text(
            '新易盛咖啡馆用户服务协议\n\n'
            '欢迎使用新易盛咖啡馆应用！\n\n'
            '1. 服务条款\n'
            '使用本应用即表示您同意遵守相关服务条款。\n\n'
            '2. 用户责任\n'
            '您需要保证提供信息的真实性和准确性。\n\n'
            '3. 服务变更\n'
            '我们保留随时修改或终止服务的权利。\n\n'
            '4. 免责声明\n'
            '在法律允许范围内，我们不承担因使用本应用而产生的损失。\n\n'
            '如有疑问，请联系我们。',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('我知道了'),
          ),
        ],
      ),
    );
  }

  void _showAbout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('关于新易盛咖啡馆'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '新易盛咖啡馆',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text('版本：1.0.0'),
            SizedBox(height: 8),
            Text('连接全球，品味生活'),
            SizedBox(height: 16),
            Text(
              '一个专为新易盛员工打造的企业咖啡馆应用，提供便捷的点餐服务和社区交流平台。',
            ),
            SizedBox(height: 16),
            Text(
              '开发团队：新易盛技术部',
              style: TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
