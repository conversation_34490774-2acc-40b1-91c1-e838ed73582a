import 'package:json_annotation/json_annotation.dart';

part 'menu.g.dart';

/// 菜单分类模型
@JsonSerializable()
class MenuCategory {
  final int id;
  final String name;
  final String icon;
  final List<MenuItem> items;

  const MenuCategory({
    required this.id,
    required this.name,
    required this.icon,
    required this.items,
  });

  factory MenuCategory.fromJson(Map<String, dynamic> json) => _$MenuCategoryFromJson(json);
  Map<String, dynamic> toJson() => _$MenuCategoryToJson(this);

  MenuCategory copyWith({
    int? id,
    String? name,
    String? icon,
    List<MenuItem>? items,
  }) {
    return MenuCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      icon: icon ?? this.icon,
      items: items ?? this.items,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MenuCategory && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MenuCategory(id: $id, name: $name, items: ${items.length})';
  }
}

/// 菜单项模型
@JsonSerializable()
class MenuItem {
  final int id;
  final String name;
  final String description;
  final double price;
  final double? originalPrice;
  final String image;
  final List<String>? images; // 新增多图片支持
  final bool? hot;
  final bool? iced;
  final bool popular;
  final String? special;

  const MenuItem({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    this.originalPrice,
    required this.image,
    this.images,
    this.hot,
    this.iced,
    required this.popular,
    this.special,
  });

  factory MenuItem.fromJson(Map<String, dynamic> json) => _$MenuItemFromJson(json);
  Map<String, dynamic> toJson() => _$MenuItemToJson(this);

  MenuItem copyWith({
    int? id,
    String? name,
    String? description,
    double? price,
    double? originalPrice,
    String? image,
    List<String>? images,
    bool? hot,
    bool? iced,
    bool? popular,
    String? special,
  }) {
    return MenuItem(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      image: image ?? this.image,
      images: images ?? this.images,
      hot: hot ?? this.hot,
      iced: iced ?? this.iced,
      popular: popular ?? this.popular,
      special: special ?? this.special,
    );
  }

  /// 是否有折扣
  bool get hasDiscount => originalPrice != null && originalPrice! > price;

  /// 折扣百分比
  double get discountPercentage {
    if (!hasDiscount) return 0;
    return ((originalPrice! - price) / originalPrice! * 100);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MenuItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MenuItem(id: $id, name: $name, price: $price)';
  }
}
