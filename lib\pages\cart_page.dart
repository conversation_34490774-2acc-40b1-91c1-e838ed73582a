import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/menu.dart';
import '../utils/app_utils.dart';

/// 购物车页面
class CartPage extends StatefulWidget {
  const CartPage({super.key});

  @override
  State<CartPage> createState() => _CartPageState();
}

class _CartPageState extends State<CartPage> {
  final List<CartItem> _cartItems = [];
  bool _selectAll = false;

  @override
  void initState() {
    super.initState();
    _loadCartItems();
  }

  void _loadCartItems() {
    // 模拟购物车数据
    _cartItems.addAll([
      CartItem(
        id: '1',
        product: MenuItem(
          id: 1,
          name: '美式咖啡',
          description: '经典美式，香醇浓郁',
          price: 25.0,
          image: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=400&h=400&fit=crop',
          popular: true,
        ),
        quantity: 2,
        specifications: '中杯 | 热饮 | 标准',
        isSelected: true,
      ),
      CartItem(
        id: '2',
        product: MenuItem(
          id: 2,
          name: '拿铁咖啡',
          description: '香滑奶泡与浓郁咖啡的完美结合',
          price: 32.0,
          originalPrice: 35.0,
          image: 'https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=400&h=400&fit=crop',
          popular: false,
        ),
        quantity: 1,
        specifications: '大杯 | 冰饮 | 少糖',
        isSelected: true,
      ),
    ]);
    _updateSelectAll();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: Text('购物车 (${_cartItems.length})'),
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _cartItems.isNotEmpty ? _clearCart : null,
            child: Text(
              '清空',
              style: TextStyle(
                color: _cartItems.isNotEmpty ? Colors.red : AppConstants.textLight,
              ),
            ),
          ),
        ],
      ),
      body: _cartItems.isEmpty ? _buildEmptyCart() : _buildCartContent(),
      bottomNavigationBar: _cartItems.isNotEmpty ? _buildBottomBar() : null,
    );
  }

  Widget _buildEmptyCart() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 80,
            color: AppConstants.textLight,
          ),
          const SizedBox(height: 16),
          const Text(
            '购物车是空的',
            style: TextStyle(
              fontSize: 18,
              color: AppConstants.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '去菜单页面选择您喜欢的商品吧',
            style: TextStyle(
              fontSize: 14,
              color: AppConstants.textLight,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('去选购'),
          ),
        ],
      ),
    );
  }

  Widget _buildCartContent() {
    return Column(
      children: [
        // 全选区域
        _buildSelectAllSection(),
        
        // 商品列表
        Expanded(
          child: ListView.separated(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            itemCount: _cartItems.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              return _buildCartItem(_cartItems[index], index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSelectAllSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      color: Colors.white,
      child: Row(
        children: [
          GestureDetector(
            onTap: _toggleSelectAll,
            child: Row(
              children: [
                Icon(
                  _selectAll ? Icons.check_circle : Icons.radio_button_unchecked,
                  color: _selectAll ? AppConstants.primaryColor : AppConstants.textSecondary,
                ),
                const SizedBox(width: 8),
                const Text(
                  '全选',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          const Spacer(),
          Text(
            '共${_cartItems.length}件商品',
            style: const TextStyle(
              fontSize: 14,
              color: AppConstants.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCartItem(CartItem item, int index) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 选择框
          GestureDetector(
            onTap: () => _toggleItemSelection(index),
            child: Icon(
              item.isSelected ? Icons.check_circle : Icons.radio_button_unchecked,
              color: item.isSelected ? AppConstants.primaryColor : AppConstants.textSecondary,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // 商品图片
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              item.product.image,
              width: 60,
              height: 60,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  width: 60,
                  height: 60,
                  color: AppConstants.backgroundColor,
                  child: const Icon(
                    Icons.local_cafe,
                    color: AppConstants.textLight,
                  ),
                );
              },
            ),
          ),
          
          const SizedBox(width: 12),
          
          // 商品信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.product.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppConstants.textPrimary,
                  ),
                ),
                
                const SizedBox(height: 4),
                
                Text(
                  item.specifications,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppConstants.textSecondary,
                  ),
                ),
                
                const SizedBox(height: 8),
                
                Row(
                  children: [
                    Flexible(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            AppUtils.formatPrice(item.product.price),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppConstants.primaryColor,
                            ),
                          ),

                          if (item.product.hasDiscount) ...[
                            const SizedBox(width: 8),
                            Flexible(
                              child: Text(
                                AppUtils.formatPrice(item.product.originalPrice!),
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: AppConstants.textLight,
                                  decoration: TextDecoration.lineThrough,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),

                    const SizedBox(width: 8),
                    
                    // 数量控制
                    _buildQuantityControl(item, index),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuantityControl(CartItem item, int index) {
    return Row(
      children: [
        GestureDetector(
          onTap: () => _decreaseQuantity(index),
          child: Container(
            width: 28,
            height: 28,
            decoration: BoxDecoration(
              border: Border.all(color: AppConstants.borderColor),
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Icon(
              Icons.remove,
              size: 16,
              color: AppConstants.textSecondary,
            ),
          ),
        ),
        
        Container(
          width: 40,
          height: 28,
          alignment: Alignment.center,
          child: Text(
            '${item.quantity}',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        
        GestureDetector(
          onTap: () => _increaseQuantity(index),
          child: Container(
            width: 28,
            height: 28,
            decoration: BoxDecoration(
              border: Border.all(color: AppConstants.borderColor),
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Icon(
              Icons.add,
              size: 16,
              color: AppConstants.textSecondary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomBar() {
    final selectedItems = _cartItems.where((item) => item.isSelected).toList();
    final totalPrice = selectedItems.fold<double>(
      0,
      (sum, item) => sum + (item.product.price * item.quantity),
    );
    final totalQuantity = selectedItems.fold<int>(
      0,
      (sum, item) => sum + item.quantity,
    );

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '合计: ${AppUtils.formatPrice(totalPrice)}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.primaryColor,
                  ),
                ),
                Text(
                  '已选择 $totalQuantity 件商品',
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppConstants.textSecondary,
                  ),
                ),
              ],
            ),
            
            const Spacer(),
            
            ElevatedButton(
              onPressed: selectedItems.isNotEmpty ? _checkout : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                '去结算 (${selectedItems.length})',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleSelectAll() {
    setState(() {
      _selectAll = !_selectAll;
      for (var item in _cartItems) {
        item.isSelected = _selectAll;
      }
    });
  }

  void _toggleItemSelection(int index) {
    setState(() {
      _cartItems[index].isSelected = !_cartItems[index].isSelected;
      _updateSelectAll();
    });
  }

  void _updateSelectAll() {
    _selectAll = _cartItems.isNotEmpty && _cartItems.every((item) => item.isSelected);
  }

  void _increaseQuantity(int index) {
    setState(() {
      _cartItems[index].quantity++;
    });
  }

  void _decreaseQuantity(int index) {
    if (_cartItems[index].quantity > 1) {
      setState(() {
        _cartItems[index].quantity--;
      });
    } else {
      _removeItem(index);
    }
  }

  void _removeItem(int index) {
    setState(() {
      _cartItems.removeAt(index);
      _updateSelectAll();
    });
  }

  void _clearCart() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清空购物车'),
        content: const Text('确定要清空购物车吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _cartItems.clear();
              });
              Navigator.of(context).pop();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _checkout() {
    AppUtils.showSuccess(context, '结算功能开发中...');
  }
}

/// 购物车商品项
class CartItem {
  final String id;
  final MenuItem product;
  int quantity;
  final String specifications;
  bool isSelected;

  CartItem({
    required this.id,
    required this.product,
    required this.quantity,
    required this.specifications,
    this.isSelected = false,
  });
}
