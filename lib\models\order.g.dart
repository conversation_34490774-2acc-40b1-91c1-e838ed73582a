// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Order _$OrderFromJson(Map<String, dynamic> json) => Order(
      id: json['id'] as String,
      status: json['status'] as String,
      statusText: json['statusText'] as String,
      items: (json['items'] as List<dynamic>)
          .map((e) => OrderItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalAmount: (json['totalAmount'] as num).toDouble(),
      orderTime: json['orderTime'] as String,
      pickupTime: json['pickupTime'] as String?,
      estimatedTime: json['estimatedTime'] as String?,
      cancelTime: json['cancelTime'] as String?,
      pickupLocation: json['pickupLocation'] as String,
    );

Map<String, dynamic> _$OrderToJson(Order instance) => <String, dynamic>{
      'id': instance.id,
      'status': instance.status,
      'statusText': instance.statusText,
      'items': instance.items,
      'totalAmount': instance.totalAmount,
      'orderTime': instance.orderTime,
      'pickupTime': instance.pickupTime,
      'estimatedTime': instance.estimatedTime,
      'cancelTime': instance.cancelTime,
      'pickupLocation': instance.pickupLocation,
    };

OrderItem _$OrderItemFromJson(Map<String, dynamic> json) => OrderItem(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      price: (json['price'] as num).toDouble(),
      quantity: (json['quantity'] as num).toInt(),
      options:
          (json['options'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$OrderItemToJson(OrderItem instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'price': instance.price,
      'quantity': instance.quantity,
      'options': instance.options,
    };
