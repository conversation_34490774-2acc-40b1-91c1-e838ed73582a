import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/community_post.dart';
import '../utils/app_utils.dart';

/// 动态详情页面
class PostDetailPage extends StatefulWidget {
  final CommunityPost post;

  const PostDetailPage({
    super.key,
    required this.post,
  });

  @override
  State<PostDetailPage> createState() => _PostDetailPageState();
}

class _PostDetailPageState extends State<PostDetailPage> {
  late bool _isLiked;
  late int _likeCount;
  bool _isFollowing = false; // 添加关注状态
  final TextEditingController _commentController = TextEditingController();
  final List<Map<String, dynamic>> _comments = [];

  @override
  void initState() {
    super.initState();
    _isLiked = false; // TODO: 从用户数据获取
    _likeCount = widget.post.likes;
    _loadComments();
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  void _loadComments() {
    // 模拟评论数据
    _comments.addAll([
      {
        'id': 1,
        'avatar': 'assets/images/avatar/avatar1.jpg',
        'username': '咖啡爱好者',
        'content': '看起来很棒！我也想试试这个拉花技巧',
        'time': '2小时前',
        'likes': 5,
      },
      {
        'id': 2,
        'avatar': 'assets/images/avatar/avatar2.jpg',
        'username': '小美',
        'content': '太厉害了，请问有教程吗？',
        'time': '1小时前',
        'likes': 3,
      },
      {
        'id': 3,
        'avatar': 'assets/images/avatar/avatar3.jpg',
        'username': '咖啡师小王',
        'content': '专业！这个拉花确实需要很多练习',
        'time': '30分钟前',
        'likes': 8,
      },
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('动态详情'),
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _sharePost,
            icon: const Icon(Icons.share),
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'report':
                  _reportPost();
                  break;
                case 'block':
                  _blockUser();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'report',
                child: Row(
                  children: [
                    Icon(Icons.report_outlined, size: 20),
                    SizedBox(width: 8),
                    Text('举报'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'block',
                child: Row(
                  children: [
                    Icon(Icons.block_outlined, size: 20),
                    SizedBox(width: 8),
                    Text('屏蔽用户'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 动态内容
                  _buildPostContent(),
                  
                  const Divider(height: 32),
                  
                  // 评论列表
                  _buildCommentSection(),
                ],
              ),
            ),
          ),
          
          // 底部操作栏
          _buildBottomBar(),
        ],
      ),
    );
  }

  Widget _buildPostContent() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用户信息
          Row(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundImage: AssetImage('assets/images/avatar/avatar${(widget.post.userId % 5) + 1}.jpg'),
              ),
              
              const SizedBox(width: 12),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '用户${widget.post.userId}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.textPrimary,
                      ),
                    ),
                    
                    const SizedBox(height: 2),
                    
                    Row(
                      children: [
                        Flexible(
                          child: Text(
                            widget.post.timestamp,
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppConstants.textSecondary,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),

                        if (widget.post.location != null) ...[
                          const SizedBox(width: 8),
                          const Icon(
                            Icons.location_on,
                            size: 12,
                            color: AppConstants.textLight,
                          ),
                          const SizedBox(width: 2),
                          Flexible(
                            child: Text(
                              widget.post.location!,
                              style: const TextStyle(
                                fontSize: 12,
                                color: AppConstants.textSecondary,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
              
              // 关注按钮
              _isFollowing
                ? ElevatedButton(
                    onPressed: _followUser,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                      minimumSize: Size.zero,
                    ),
                    child: const Text(
                      '已关注',
                      style: TextStyle(fontSize: 12),
                    ),
                  )
                : OutlinedButton(
                    onPressed: _followUser,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppConstants.primaryColor,
                      side: const BorderSide(color: AppConstants.primaryColor),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                      minimumSize: Size.zero,
                    ),
                    child: const Text(
                      '关注',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 动态内容
          Text(
            widget.post.content,
            style: const TextStyle(
              fontSize: 16,
              color: AppConstants.textPrimary,
              height: 1.5,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 图片网格
          if (widget.post.images.isNotEmpty)
            _buildImageGrid(widget.post.images),
          
          const SizedBox(height: 16),
          
          // 互动数据
          Row(
            children: [
              _buildInteractionItem(
                icon: Icons.favorite,
                count: _likeCount,
                color: _isLiked ? Colors.red : AppConstants.textSecondary,
              ),
              
              const SizedBox(width: 24),
              
              _buildInteractionItem(
                icon: Icons.comment_outlined,
                count: widget.post.comments + _comments.length,
                color: AppConstants.textSecondary,
              ),
              
              const SizedBox(width: 24),
              
              _buildInteractionItem(
                icon: Icons.share_outlined,
                count: 0,
                color: AppConstants.textSecondary,
                showCount: false,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildImageGrid(List<String> images) {
    if (images.isEmpty) return const SizedBox.shrink();
    
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: images.length == 1 ? 1 : (images.length == 2 ? 2 : 3),
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: images.length > 9 ? 9 : images.length,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () => _viewImage(images, index),
          child: Stack(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.asset(
                  _getLocalImagePath(index),
                  width: double.infinity,
                  height: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: AppConstants.backgroundColor,
                      child: const Icon(
                        Icons.image_not_supported,
                        color: AppConstants.textLight,
                      ),
                    );
                  },
                ),
              ),
              
              if (index == 8 && images.length > 9)
                Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Text(
                      '+${images.length - 9}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInteractionItem({
    required IconData icon,
    required int count,
    required Color color,
    bool showCount = true,
  }) {
    return Row(
      children: [
        Icon(icon, size: 20, color: color),
        if (showCount && count > 0) ...[
          const SizedBox(width: 4),
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 14,
              color: color,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCommentSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '评论 (${widget.post.comments + _comments.length})',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimary,
            ),
          ),
          
          const SizedBox(height: 16),
          
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _comments.length,
            separatorBuilder: (context, index) => const SizedBox(height: 16),
            itemBuilder: (context, index) {
              final comment = _comments[index];
              return _buildCommentItem(comment);
            },
          ),
          
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildCommentItem(Map<String, dynamic> comment) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CircleAvatar(
          radius: 16,
          backgroundImage: AssetImage(comment['avatar']),
        ),
        
        const SizedBox(width: 12),
        
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    comment['username'],
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppConstants.textPrimary,
                    ),
                  ),
                  
                  const Spacer(),
                  
                  Text(
                    comment['time'],
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppConstants.textSecondary,
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 4),
              
              Text(
                comment['content'],
                style: const TextStyle(
                  fontSize: 14,
                  color: AppConstants.textPrimary,
                  height: 1.4,
                ),
              ),
              
              const SizedBox(height: 8),
              
              Row(
                children: [
                  GestureDetector(
                    onTap: () => _likeComment(comment['id']),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.favorite_border,
                          size: 16,
                          color: AppConstants.textSecondary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          comment['likes'].toString(),
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppConstants.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  GestureDetector(
                    onTap: () => _replyComment(comment['username']),
                    child: const Text(
                      '回复',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppConstants.textSecondary,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBottomBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 评论输入框
            Expanded(
              child: TextField(
                controller: _commentController,
                decoration: InputDecoration(
                  hintText: '写评论...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20),
                    borderSide: const BorderSide(color: AppConstants.borderColor),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                ),
              ),
            ),
            
            const SizedBox(width: 12),
            
            // 点赞按钮
            GestureDetector(
              onTap: _toggleLike,
              child: Container(
                padding: const EdgeInsets.all(8),
                child: Icon(
                  _isLiked ? Icons.favorite : Icons.favorite_border,
                  color: _isLiked ? Colors.red : AppConstants.textSecondary,
                  size: 24,
                ),
              ),
            ),
            
            // 发送按钮
            GestureDetector(
              onTap: _sendComment,
              child: Container(
                padding: const EdgeInsets.all(8),
                child: const Icon(
                  Icons.send,
                  color: AppConstants.primaryColor,
                  size: 24,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleLike() {
    setState(() {
      _isLiked = !_isLiked;
      _likeCount += _isLiked ? 1 : -1;
    });
    AppUtils.vibrate();
  }

  void _sendComment() {
    if (_commentController.text.trim().isEmpty) return;
    
    setState(() {
      _comments.insert(0, {
        'id': DateTime.now().millisecondsSinceEpoch,
        'avatar': 'assets/images/avatar/avatar1.jpg',
        'username': '我',
        'content': _commentController.text.trim(),
        'time': '刚刚',
        'likes': 0,
      });
    });
    
    _commentController.clear();
    AppUtils.showSuccess(context, '评论发送成功');
  }

  void _likeComment(int commentId) {
    // TODO: 实现评论点赞逻辑
    AppUtils.vibrate();
  }

  void _replyComment(String username) {
    _commentController.text = '@$username ';
  }

  void _followUser() {
    setState(() {
      _isFollowing = !_isFollowing;
    });
    AppUtils.showSuccess(context, _isFollowing ? '关注成功' : '已取消关注');
    AppUtils.vibrate();
  }

  void _sharePost() {
    AppUtils.showSuccess(context, '分享功能开发中...');
  }

  void _reportPost() {
    AppUtils.showSuccess(context, '举报已提交');
  }

  void _blockUser() {
    AppUtils.showSuccess(context, '用户已屏蔽');
  }

  void _viewImage(List<String> images, int index) {
    // TODO: 实现图片查看器
    AppUtils.showSuccess(context, '图片查看功能开发中...');
  }

  String _getLocalImagePath(int index) {
    // 根据索引返回本地社区图片路径
    final imageIndex = (index % 4) + 1; // 循环使用4张图片
    return 'assets/images/community/community$imageIndex.jpg';
  }
}
