import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/user.dart';
import '../utils/app_utils.dart';

/// 粉丝页面
class FollowersPage extends StatefulWidget {
  final User user;
  
  const FollowersPage({
    super.key,
    required this.user,
  });

  @override
  State<FollowersPage> createState() => _FollowersPageState();
}

class _FollowersPageState extends State<FollowersPage> {
  List<FollowerUser> _followers = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadFollowers();
  }

  void _loadFollowers() {
    setState(() {
      _isLoading = true;
    });

    // 模拟网络延迟和数据
    Future.delayed(const Duration(milliseconds: 500), () {
      setState(() {
        _followers = _generateMockFollowers();
        _isLoading = false;
      });
    });
  }

  List<FollowerUser> _generateMockFollowers() {
    return [
      FollowerUser(
        id: '1',
        name: '咖啡爱好者小王',
        avatar: 'assets/images/avatar1.jpg',
        bio: '每天一杯咖啡，生活更美好',
        isFollowing: true,
        followTime: DateTime.now().subtract(const Duration(days: 5)),
      ),
      FollowerUser(
        id: '2',
        name: '拿铁控',
        avatar: 'assets/images/avatar2.jpg',
        bio: '拿铁是我的最爱，没有之一',
        isFollowing: false,
        followTime: DateTime.now().subtract(const Duration(days: 12)),
      ),
      FollowerUser(
        id: '3',
        name: '美式党',
        avatar: 'assets/images/avatar3.jpg',
        bio: '纯正美式，简单生活',
        isFollowing: true,
        followTime: DateTime.now().subtract(const Duration(days: 20)),
      ),
      FollowerUser(
        id: '4',
        name: '手冲达人',
        avatar: 'assets/images/avatar4.jpg',
        bio: '手冲咖啡，品味人生',
        isFollowing: false,
        followTime: DateTime.now().subtract(const Duration(days: 30)),
      ),
      FollowerUser(
        id: '5',
        name: '咖啡新手',
        avatar: 'assets/images/avatar5.jpg',
        bio: '刚开始喝咖啡，请多指教',
        isFollowing: true,
        followTime: DateTime.now().subtract(const Duration(days: 45)),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: Text('${widget.user.name}的粉丝'),
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _followers.isEmpty
              ? _buildEmptyState()
              : _buildFollowersList(),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: AppConstants.textLight,
          ),
          SizedBox(height: 16),
          Text(
            '还没有粉丝',
            style: TextStyle(
              color: AppConstants.textLight,
              fontSize: 16,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '多发布优质内容来吸引粉丝吧～',
            style: TextStyle(
              color: AppConstants.textLight,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFollowersList() {
    return RefreshIndicator(
      onRefresh: () async {
        _loadFollowers();
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _followers.length,
        itemBuilder: (context, index) {
          final follower = _followers[index];
          return _buildFollowerCard(follower);
        },
      ),
    );
  }

  Widget _buildFollowerCard(FollowerUser follower) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // 头像
            CircleAvatar(
              radius: 25,
              backgroundImage: AssetImage(follower.avatar),
              onBackgroundImageError: (exception, stackTrace) {},
              child: follower.avatar.isEmpty
                  ? const Icon(Icons.person, size: 25)
                  : null,
            ),
            
            const SizedBox(width: 12),
            
            // 用户信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    follower.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppConstants.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    follower.bio,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppConstants.textSecondary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '关注时间：${AppUtils.formatTime(follower.followTime)}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppConstants.textLight,
                    ),
                  ),
                ],
              ),
            ),
            
            // 关注按钮
            _buildFollowButton(follower),
          ],
        ),
      ),
    );
  }

  Widget _buildFollowButton(FollowerUser follower) {
    return GestureDetector(
      onTap: () => _toggleFollow(follower),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: follower.isFollowing ? Colors.grey[200] : AppConstants.primaryColor,
          borderRadius: BorderRadius.circular(20),
          border: follower.isFollowing 
              ? Border.all(color: Colors.grey[300]!)
              : null,
        ),
        child: Text(
          follower.isFollowing ? '已关注' : '关注',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: follower.isFollowing ? AppConstants.textSecondary : Colors.white,
          ),
        ),
      ),
    );
  }

  void _toggleFollow(FollowerUser follower) {
    setState(() {
      follower.isFollowing = !follower.isFollowing;
    });
    
    AppUtils.showSuccess(
      context, 
      follower.isFollowing ? '已关注${follower.name}' : '已取消关注${follower.name}',
    );
  }
}

/// 粉丝用户模型
class FollowerUser {
  final String id;
  final String name;
  final String avatar;
  final String bio;
  bool isFollowing;
  final DateTime followTime;

  FollowerUser({
    required this.id,
    required this.name,
    required this.avatar,
    required this.bio,
    required this.isFollowing,
    required this.followTime,
  });
}
