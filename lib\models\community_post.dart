import 'package:json_annotation/json_annotation.dart';

part 'community_post.g.dart';

/// 社区动态模型
@JsonSerializable()
class CommunityPost {
  final int id;
  final int userId;
  final String content;
  final List<String> images;
  final String timestamp;
  final int likes;
  final int comments;
  final int shares;
  final bool liked;
  final String? location;

  const CommunityPost({
    required this.id,
    required this.userId,
    required this.content,
    required this.images,
    required this.timestamp,
    required this.likes,
    required this.comments,
    required this.shares,
    required this.liked,
    this.location,
  });

  factory CommunityPost.fromJson(Map<String, dynamic> json) => _$CommunityPostFromJson(json);
  Map<String, dynamic> toJson() => _$CommunityPostToJson(this);

  CommunityPost copyWith({
    int? id,
    int? userId,
    String? content,
    List<String>? images,
    String? timestamp,
    int? likes,
    int? comments,
    int? shares,
    bool? liked,
    String? location,
  }) {
    return CommunityPost(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      content: content ?? this.content,
      images: images ?? this.images,
      timestamp: timestamp ?? this.timestamp,
      likes: likes ?? this.likes,
      comments: comments ?? this.comments,
      shares: shares ?? this.shares,
      liked: liked ?? this.liked,
      location: location ?? this.location,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommunityPost && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CommunityPost(id: $id, userId: $userId, content: ${content.substring(0, content.length > 50 ? 50 : content.length)}...)';
  }
}

/// 评论模型
@JsonSerializable()
class Comment {
  final int id;
  final int userId;
  final String content;
  final String timestamp;
  final int likes;

  const Comment({
    required this.id,
    required this.userId,
    required this.content,
    required this.timestamp,
    required this.likes,
  });

  factory Comment.fromJson(Map<String, dynamic> json) => _$CommentFromJson(json);
  Map<String, dynamic> toJson() => _$CommentToJson(this);

  Comment copyWith({
    int? id,
    int? userId,
    String? content,
    String? timestamp,
    int? likes,
  }) {
    return Comment(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      content: content ?? this.content,
      timestamp: timestamp ?? this.timestamp,
      likes: likes ?? this.likes,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Comment && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Comment(id: $id, userId: $userId, content: ${content.substring(0, content.length > 30 ? 30 : content.length)}...)';
  }
}
