import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'app_constants.dart';

/// 应用主题配置
class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppConstants.primaryColor,
        brightness: Brightness.light,
        primary: AppConstants.primaryColor,
        secondary: AppConstants.secondaryColor,
        surface: AppConstants.cardBackground,
        error: AppConstants.errorColor,
      ),
      
      // AppBar主题
      appBarTheme: const AppBarTheme(
        backgroundColor: AppConstants.backgroundColor,
        foregroundColor: AppConstants.textPrimary,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: AppConstants.textPrimary,
          fontSize: AppConstants.fontSizeLarge,
          fontWeight: FontWeight.w600,
        ),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
      ),
      
      // 底部导航栏主题
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: AppConstants.cardBackground,
        selectedItemColor: AppConstants.primaryColor,
        unselectedItemColor: AppConstants.textLight,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
        selectedLabelStyle: TextStyle(
          fontSize: AppConstants.fontSizeSmall,
          fontWeight: FontWeight.w500,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: AppConstants.fontSizeSmall,
        ),
      ),
      
      // 卡片主题
      cardTheme: const CardThemeData(
        color: AppConstants.cardBackground,
        elevation: 2,
        shadowColor: Colors.black12,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(AppConstants.radiusMedium)),
        ),
      ),
      
      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppConstants.primaryColor,
          foregroundColor: Colors.white,
          elevation: 2,
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingLarge,
            vertical: AppConstants.paddingMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
          ),
          textStyle: const TextStyle(
            fontSize: AppConstants.fontSizeMedium,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      
      // 文本按钮主题
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppConstants.primaryColor,
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingMedium,
            vertical: AppConstants.paddingSmall,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          ),
        ),
      ),
      
      // 输入框主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppConstants.cardBackground,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          borderSide: const BorderSide(color: AppConstants.borderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          borderSide: const BorderSide(color: AppConstants.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          borderSide: const BorderSide(color: AppConstants.primaryColor, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingMedium,
        ),
        hintStyle: const TextStyle(
          color: AppConstants.textLight,
          fontSize: AppConstants.fontSizeMedium,
        ),
      ),
      
      // 文本主题
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          color: AppConstants.textPrimary,
          fontSize: 32,
          fontWeight: FontWeight.bold,
        ),
        displayMedium: TextStyle(
          color: AppConstants.textPrimary,
          fontSize: 28,
          fontWeight: FontWeight.bold,
        ),
        displaySmall: TextStyle(
          color: AppConstants.textPrimary,
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
        headlineLarge: TextStyle(
          color: AppConstants.textPrimary,
          fontSize: AppConstants.fontSizeXXLarge,
          fontWeight: FontWeight.w600,
        ),
        headlineMedium: TextStyle(
          color: AppConstants.textPrimary,
          fontSize: AppConstants.fontSizeXLarge,
          fontWeight: FontWeight.w600,
        ),
        headlineSmall: TextStyle(
          color: AppConstants.textPrimary,
          fontSize: AppConstants.fontSizeLarge,
          fontWeight: FontWeight.w600,
        ),
        titleLarge: TextStyle(
          color: AppConstants.textPrimary,
          fontSize: AppConstants.fontSizeLarge,
          fontWeight: FontWeight.w500,
        ),
        titleMedium: TextStyle(
          color: AppConstants.textPrimary,
          fontSize: AppConstants.fontSizeMedium,
          fontWeight: FontWeight.w500,
        ),
        titleSmall: TextStyle(
          color: AppConstants.textSecondary,
          fontSize: AppConstants.fontSizeMedium,
          fontWeight: FontWeight.w500,
        ),
        bodyLarge: TextStyle(
          color: AppConstants.textPrimary,
          fontSize: AppConstants.fontSizeLarge,
        ),
        bodyMedium: TextStyle(
          color: AppConstants.textPrimary,
          fontSize: AppConstants.fontSizeMedium,
        ),
        bodySmall: TextStyle(
          color: AppConstants.textSecondary,
          fontSize: AppConstants.fontSizeSmall,
        ),
        labelLarge: TextStyle(
          color: AppConstants.textPrimary,
          fontSize: AppConstants.fontSizeMedium,
          fontWeight: FontWeight.w500,
        ),
        labelMedium: TextStyle(
          color: AppConstants.textSecondary,
          fontSize: AppConstants.fontSizeSmall,
          fontWeight: FontWeight.w500,
        ),
        labelSmall: TextStyle(
          color: AppConstants.textLight,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
      
      // 分割线主题
      dividerTheme: const DividerThemeData(
        color: AppConstants.borderColor,
        thickness: 1,
        space: 1,
      ),
      
      // 图标主题
      iconTheme: const IconThemeData(
        color: AppConstants.textSecondary,
        size: AppConstants.iconSizeMedium,
      ),
    );
  }
}
