import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../utils/app_utils.dart';

/// 通知页面
class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> with TickerProviderStateMixin {
  late TabController _tabController;
  
  // 模拟通知数据
  final List<NotificationItem> _systemNotifications = [
    NotificationItem(
      id: '1',
      title: '系统维护通知',
      content: '系统将于今晚23:00-01:00进行维护升级，期间可能影响部分功能使用，敬请谅解。',
      time: DateTime.now().subtract(const Duration(hours: 2)),
      type: NotificationType.system,
      isRead: false,
    ),
    NotificationItem(
      id: '2',
      title: '新功能上线',
      content: 'AI助手小盛现已上线！快来体验智能点餐和个性化推荐服务吧～',
      time: DateTime.now().subtract(const Duration(days: 1)),
      type: NotificationType.system,
      isRead: true,
    ),
    NotificationItem(
      id: '3',
      title: '隐私政策更新',
      content: '我们更新了隐私政策，请查看最新条款以了解我们如何保护您的个人信息。',
      time: DateTime.now().subtract(const Duration(days: 3)),
      type: NotificationType.system,
      isRead: true,
    ),
  ];

  final List<NotificationItem> _activityNotifications = [
    NotificationItem(
      id: '4',
      title: '春季限定活动开始',
      content: '春季限定饮品现已上线！樱花拿铁、抹茶芝士蛋糕等你来品尝，活动期间享8.5折优惠。',
      time: DateTime.now().subtract(const Duration(hours: 6)),
      type: NotificationType.activity,
      isRead: false,
    ),
    NotificationItem(
      id: '5',
      title: '会员积分翻倍活动',
      content: '本周末消费积分翻倍！快来享受更多优惠，积分可兑换精美礼品。',
      time: DateTime.now().subtract(const Duration(days: 2)),
      type: NotificationType.activity,
      isRead: false,
    ),
    NotificationItem(
      id: '6',
      title: '生日特惠来袭',
      content: '生日月专享：免费升杯、生日蛋糕券、专属折扣等你来领取！',
      time: DateTime.now().subtract(const Duration(days: 5)),
      type: NotificationType.activity,
      isRead: true,
    ),
  ];

  final List<NotificationItem> _orderNotifications = [
    NotificationItem(
      id: '7',
      title: '订单制作完成',
      content: '您的订单【美式咖啡 x1】已制作完成，请及时到店取餐。订单号：202403150001',
      time: DateTime.now().subtract(const Duration(minutes: 15)),
      type: NotificationType.order,
      isRead: false,
    ),
    NotificationItem(
      id: '8',
      title: '订单支付成功',
      content: '您的订单支付成功，预计15分钟后可取餐。感谢您的光临！',
      time: DateTime.now().subtract(const Duration(hours: 1)),
      type: NotificationType.order,
      isRead: true,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text('通知'),
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _markAllAsRead,
            child: const Text(
              '全部已读',
              style: TextStyle(
                color: AppConstants.primaryColor,
                fontSize: 14,
              ),
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('系统'),
                  if (_getUnreadCount(_systemNotifications) > 0) ...[
                    const SizedBox(width: 4),
                    _buildUnreadBadge(_getUnreadCount(_systemNotifications)),
                  ],
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('活动'),
                  if (_getUnreadCount(_activityNotifications) > 0) ...[
                    const SizedBox(width: 4),
                    _buildUnreadBadge(_getUnreadCount(_activityNotifications)),
                  ],
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('订单'),
                  if (_getUnreadCount(_orderNotifications) > 0) ...[
                    const SizedBox(width: 4),
                    _buildUnreadBadge(_getUnreadCount(_orderNotifications)),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildNotificationList(_systemNotifications),
          _buildNotificationList(_activityNotifications),
          _buildNotificationList(_orderNotifications),
        ],
      ),
    );
  }

  Widget _buildUnreadBadge(int count) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: const BoxDecoration(
        color: Colors.red,
        shape: BoxShape.circle,
      ),
      child: Text(
        count > 99 ? '99+' : count.toString(),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildNotificationList(List<NotificationItem> notifications) {
    if (notifications.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.notifications_none,
              size: 64,
              color: AppConstants.textLight,
            ),
            SizedBox(height: 16),
            Text(
              '暂无通知',
              style: TextStyle(
                color: AppConstants.textLight,
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: notifications.length,
      itemBuilder: (context, index) {
        final notification = notifications[index];
        return _buildNotificationItem(notification);
      },
    );
  }

  Widget _buildNotificationItem(NotificationItem notification) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _markAsRead(notification),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 通知图标
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _getNotificationColor(notification.type).withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getNotificationIcon(notification.type),
                  color: _getNotificationColor(notification.type),
                  size: 20,
                ),
              ),
              
              const SizedBox(width: 12),
              
              // 通知内容
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            notification.title,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: notification.isRead ? FontWeight.w500 : FontWeight.w600,
                              color: AppConstants.textPrimary,
                            ),
                          ),
                        ),
                        if (!notification.isRead)
                          Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                          ),
                      ],
                    ),
                    
                    const SizedBox(height: 4),
                    
                    Text(
                      notification.content,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppConstants.textSecondary,
                        height: 1.4,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 8),
                    
                    Text(
                      AppUtils.formatTime(notification.time),
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppConstants.textLight,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getNotificationIcon(NotificationType type) {
    switch (type) {
      case NotificationType.system:
        return Icons.settings;
      case NotificationType.activity:
        return Icons.local_activity;
      case NotificationType.order:
        return Icons.receipt;
    }
  }

  Color _getNotificationColor(NotificationType type) {
    switch (type) {
      case NotificationType.system:
        return Colors.blue;
      case NotificationType.activity:
        return AppConstants.primaryColor;
      case NotificationType.order:
        return Colors.green;
    }
  }

  int _getUnreadCount(List<NotificationItem> notifications) {
    return notifications.where((n) => !n.isRead).length;
  }

  void _markAsRead(NotificationItem notification) {
    if (!notification.isRead) {
      setState(() {
        notification.isRead = true;
      });
    }
  }

  void _markAllAsRead() {
    setState(() {
      for (var notification in _systemNotifications) {
        notification.isRead = true;
      }
      for (var notification in _activityNotifications) {
        notification.isRead = true;
      }
      for (var notification in _orderNotifications) {
        notification.isRead = true;
      }
    });
    AppUtils.showSuccess(context, '已标记全部通知为已读');
  }
}

/// 通知类型枚举
enum NotificationType {
  system,   // 系统通知
  activity, // 活动通知
  order,    // 订单通知
}

/// 通知项模型
class NotificationItem {
  final String id;
  final String title;
  final String content;
  final DateTime time;
  final NotificationType type;
  bool isRead;

  NotificationItem({
    required this.id,
    required this.title,
    required this.content,
    required this.time,
    required this.type,
    this.isRead = false,
  });
}
