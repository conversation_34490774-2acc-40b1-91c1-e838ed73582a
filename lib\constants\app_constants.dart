import 'package:flutter/material.dart';

/// 应用常量定义
class AppConstants {
  // 应用信息
  static const String appName = '不止咖啡';
  static const String appVersion = '1.0.0';
  
  // 主题色彩
  static const Color primaryColor = Color(0xFF8B4513); // 咖啡色
  static const Color secondaryColor = Color(0xFFD2691E); // 浅咖啡色
  static const Color accentColor = Color(0xFFF4A460); // 沙棕色
  static const Color backgroundColor = Color(0xFFF5F5F5);
  static const Color cardBackground = Color(0xFFFFFFFF);
  static const Color textPrimary = Color(0xFF333333);
  static const Color textSecondary = Color(0xFF666666);
  static const Color textLight = Color(0xFF999999);
  static const Color borderColor = Color(0xFFE5E5E5);
  static const Color successColor = Color(0xFF52C41A);
  static const Color warningColor = Color(0xFFFAAD14);
  static const Color errorColor = Color(0xFFFF4D4F);
  
  // 订单状态
  static const String orderStatusPending = 'pending';
  static const String orderStatusPaid = 'paid';
  static const String orderStatusPreparing = 'preparing';
  static const String orderStatusReady = 'ready';
  static const String orderStatusCompleted = 'completed';
  static const String orderStatusCancelled = 'cancelled';
  
  // 动态类型
  static const String postTypeText = 'text';
  static const String postTypeImage = 'image';
  static const String postTypeVideo = 'video';
  static const String postTypeLink = 'link';
  
  // 隐私设置
  static const String privacyPublic = 'public';
  static const String privacyDepartment = 'department';
  static const String privacyCampus = 'campus';
  
  // 页面路由
  static const String routeHome = '/';
  static const String routeMenu = '/menu';
  static const String routeOrder = '/order';
  static const String routeCommunity = '/community';
  static const String routeProfile = '/profile';
  static const String routeProductDetail = '/product-detail';
  static const String routeOrderDetail = '/order-detail';
  static const String routePostDetail = '/post-detail';
  static const String routePublishPost = '/publish-post';
  static const String routeXiaosheng = '/xiaosheng';
  static const String routeAIAssistant = '/ai-assistant';
  
  // 尺寸常量
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;
  
  // 字体大小
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 14.0;
  static const double fontSizeLarge = 16.0;
  static const double fontSizeXLarge = 18.0;
  static const double fontSizeXXLarge = 20.0;
  
  // 动画时长
  static const Duration animationDurationShort = Duration(milliseconds: 200);
  static const Duration animationDurationMedium = Duration(milliseconds: 300);
  static const Duration animationDurationLong = Duration(milliseconds: 500);
}

/// 底部导航栏配置
class TabBarConfig {
  static const List<Map<String, dynamic>> tabs = [
    {
      'label': '首页',
      'icon': Icons.home_outlined,
      'activeIcon': Icons.home,
      'route': AppConstants.routeHome,
    },
    {
      'label': '菜单',
      'icon': Icons.restaurant_menu_outlined,
      'activeIcon': Icons.restaurant_menu,
      'route': AppConstants.routeMenu,
    },
    {
      'label': '小盛',
      'icon': Icons.smart_toy_outlined,
      'activeIcon': Icons.smart_toy,
      'route': AppConstants.routeAIAssistant,
    },
    {
      'label': '社区',
      'icon': Icons.people_outline,
      'activeIcon': Icons.people,
      'route': AppConstants.routeCommunity,
    },
    {
      'label': '我的',
      'icon': Icons.person_outline,
      'activeIcon': Icons.person,
      'route': AppConstants.routeProfile,
    },
  ];
}
