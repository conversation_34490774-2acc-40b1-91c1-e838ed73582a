import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 应用全局服务
class AppService extends ChangeNotifier {
  late SharedPreferences _prefs;
  bool _isInitialized = false;
  
  // 用户相关状态
  bool _isLoggedIn = false;
  String? _currentUserId;
  
  // 应用状态
  int _currentTabIndex = 0;
  int _communityTabIndex = 0; // 社区页面的标签索引

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isLoggedIn => _isLoggedIn;
  String? get currentUserId => _currentUserId;
  int get currentTabIndex => _currentTabIndex;
  int get communityTabIndex => _communityTabIndex;
  
  /// 初始化服务
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      
      // 加载用户登录状态
      _isLoggedIn = _prefs.getBool('is_logged_in') ?? false;
      _currentUserId = _prefs.getString('current_user_id');
      
      // 加载应用设置
      _currentTabIndex = _prefs.getInt('current_tab_index') ?? 0;
      
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('AppService初始化失败: $e');
    }
  }
  
  /// 设置当前标签页索引
  void setCurrentTabIndex(int index) {
    if (_currentTabIndex != index) {
      _currentTabIndex = index;
      _prefs.setInt('current_tab_index', index);
      notifyListeners();
    }
  }

  /// 设置社区页面标签索引
  void setCommunityTabIndex(int index) {
    if (_communityTabIndex != index) {
      _communityTabIndex = index;
      notifyListeners();
    }
  }
  
  /// 用户登录
  Future<void> login(String userId) async {
    _isLoggedIn = true;
    _currentUserId = userId;
    
    await _prefs.setBool('is_logged_in', true);
    await _prefs.setString('current_user_id', userId);
    
    notifyListeners();
  }
  
  /// 用户登出
  Future<void> logout() async {
    _isLoggedIn = false;
    _currentUserId = null;
    
    await _prefs.setBool('is_logged_in', false);
    await _prefs.remove('current_user_id');
    
    notifyListeners();
  }
  
  /// 获取存储的字符串值
  String? getString(String key) {
    return _prefs.getString(key);
  }
  
  /// 设置存储的字符串值
  Future<void> setString(String key, String value) async {
    await _prefs.setString(key, value);
  }
  
  /// 获取存储的布尔值
  bool getBool(String key, {bool defaultValue = false}) {
    return _prefs.getBool(key) ?? defaultValue;
  }
  
  /// 设置存储的布尔值
  Future<void> setBool(String key, bool value) async {
    await _prefs.setBool(key, value);
  }
  
  /// 获取存储的整数值
  int getInt(String key, {int defaultValue = 0}) {
    return _prefs.getInt(key) ?? defaultValue;
  }
  
  /// 设置存储的整数值
  Future<void> setInt(String key, int value) async {
    await _prefs.setInt(key, value);
  }
  
  /// 清除所有存储数据
  Future<void> clearAll() async {
    await _prefs.clear();
    _isLoggedIn = false;
    _currentUserId = null;
    _currentTabIndex = 0;
    notifyListeners();
  }
}
