import 'package:intl/intl.dart';

/// 日期时间工具类
class AppDateUtils {
  /// 格式化时间戳为相对时间
  static String formatRelativeTime(String timestamp) {
    try {
      final now = DateTime.now();
      final time = DateTime.parse(timestamp.replaceAll(' ', 'T'));
      final difference = now.difference(time);

      if (difference.inMinutes < 1) {
        return '刚刚';
      } else if (difference.inMinutes < 60) {
        return '${difference.inMinutes}分钟前';
      } else if (difference.inHours < 24) {
        return '${difference.inHours}小时前';
      } else if (difference.inDays < 7) {
        return '${difference.inDays}天前';
      } else {
        return DateFormat('MM-dd').format(time);
      }
    } catch (e) {
      return timestamp;
    }
  }

  /// 格式化日期
  static String formatDate(String dateString, {String pattern = 'yyyy-MM-dd'}) {
    try {
      final date = DateTime.parse(dateString);
      return DateFormat(pattern).format(date);
    } catch (e) {
      return dateString;
    }
  }

  /// 格式化时间
  static String formatTime(String timeString, {String pattern = 'HH:mm'}) {
    try {
      final time = DateFormat('HH:mm').parse(timeString);
      return DateFormat(pattern).format(time);
    } catch (e) {
      return timeString;
    }
  }

  /// 格式化日期时间
  static String formatDateTime(String dateTimeString, {String pattern = 'yyyy-MM-dd HH:mm'}) {
    try {
      final dateTime = DateTime.parse(dateTimeString.replaceAll(' ', 'T'));
      return DateFormat(pattern).format(dateTime);
    } catch (e) {
      return dateTimeString;
    }
  }

  /// 获取今天的日期字符串
  static String getTodayString({String pattern = 'yyyy-MM-dd'}) {
    return DateFormat(pattern).format(DateTime.now());
  }

  /// 获取当前时间字符串
  static String getCurrentTimeString({String pattern = 'HH:mm'}) {
    return DateFormat(pattern).format(DateTime.now());
  }

  /// 获取当前日期时间字符串
  static String getCurrentDateTimeString({String pattern = 'yyyy-MM-dd HH:mm'}) {
    return DateFormat(pattern).format(DateTime.now());
  }

  /// 判断是否是今天
  static bool isToday(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      final now = DateTime.now();
      return date.year == now.year && 
             date.month == now.month && 
             date.day == now.day;
    } catch (e) {
      return false;
    }
  }

  /// 判断是否是昨天
  static bool isYesterday(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      final yesterday = DateTime.now().subtract(const Duration(days: 1));
      return date.year == yesterday.year && 
             date.month == yesterday.month && 
             date.day == yesterday.day;
    } catch (e) {
      return false;
    }
  }

  /// 获取友好的日期显示
  static String getFriendlyDate(String dateString) {
    if (isToday(dateString)) {
      return '今天';
    } else if (isYesterday(dateString)) {
      return '昨天';
    } else {
      return formatDate(dateString, pattern: 'MM月dd日');
    }
  }
}
