import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/activity.dart';
import '../utils/app_utils.dart';

/// 活动详情页面
class ActivityDetailPage extends StatefulWidget {
  final Activity activity;

  const ActivityDetailPage({
    super.key,
    required this.activity,
  });

  @override
  State<ActivityDetailPage> createState() => _ActivityDetailPageState();
}

class _ActivityDetailPageState extends State<ActivityDetailPage> {
  bool _isParticipating = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // TODO: 检查用户是否已参与活动
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: CustomScrollView(
        slivers: [
          // 顶部图片区域
          _buildSliverAppBar(),
          
          // 活动信息
          SliverToBoxAdapter(
            child: _buildActivityInfo(),
          ),
          
          // 活动详情
          SliverToBoxAdapter(
            child: _buildActivityDetails(),
          ),
          
          // 参与规则
          SliverToBoxAdapter(
            child: _buildRules(),
          ),
          
          // 参与者列表
          SliverToBoxAdapter(
            child: _buildParticipants(),
          ),
          
          // 底部间距
          const SliverToBoxAdapter(
            child: SizedBox(height: 100),
          ),
        ],
      ),
      
      // 底部操作栏
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 250,
      pinned: true,
      backgroundColor: Colors.white,
      foregroundColor: AppConstants.textPrimary,
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          fit: StackFit.expand,
          children: [
            // 活动图片
            Image.asset(
              widget.activity.image,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: AppConstants.backgroundColor,
                  child: const Icon(
                    Icons.event,
                    size: 80,
                    color: AppConstants.textLight,
                  ),
                );
              },
            ),
            
            // 渐变遮罩
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.3),
                  ],
                ),
              ),
            ),
            
            // 状态标签
            Positioned(
              top: 100,
              right: 20,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: _getStatusColor(),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  _getStatusText(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      actions: [
        IconButton(
          onPressed: _shareActivity,
          icon: const Icon(Icons.share),
        ),
      ],
    );
  }

  Widget _buildActivityInfo() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 活动标题
          Text(
            widget.activity.title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimary,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 活动信息卡片
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppConstants.backgroundColor,
              borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
            ),
            child: Column(
              children: [
                _buildInfoRow(
                  icon: Icons.access_time,
                  title: '活动时间',
                  content: '${widget.activity.date} ${widget.activity.time}',
                ),
                
                const SizedBox(height: 12),
                
                _buildInfoRow(
                  icon: Icons.location_on,
                  title: '活动地点',
                  content: widget.activity.location,
                ),
                
                const SizedBox(height: 12),
                
                _buildInfoRow(
                  icon: Icons.people,
                  title: '参与人数',
                  content: '${widget.activity.participants}/${widget.activity.maxParticipants}人',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String title,
    required String content,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: AppConstants.primaryColor,
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppConstants.textSecondary,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            content,
            style: const TextStyle(
              fontSize: 14,
              color: AppConstants.textPrimary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActivityDetails() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        border: Border.all(color: AppConstants.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '活动详情',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimary,
            ),
          ),
          
          const SizedBox(height: 12),
          
          Text(
            widget.activity.description,
            style: const TextStyle(
              fontSize: 15,
              color: AppConstants.textSecondary,
              height: 1.6,
            ),
          ),
          
          const SizedBox(height: 16),
          
          const Text(
            '活动亮点：\n'
            '• 专业咖啡师现场指导\n'
            '• 免费提供咖啡豆和器具\n'
            '• 学会基础拉花技巧\n'
            '• 获得专属活动证书\n'
            '• 结识志同道合的朋友',
            style: TextStyle(
              fontSize: 14,
              color: AppConstants.textSecondary,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRules() {
    return Container(
      margin: const EdgeInsets.all(AppConstants.paddingLarge),
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '参与规则',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimary,
            ),
          ),
          
          SizedBox(height: 12),
          
          Text(
            '1. 活动免费参与，需提前报名\n'
            '2. 请准时到达活动现场\n'
            '3. 活动期间请遵守现场秩序\n'
            '4. 如有特殊情况无法参加，请提前24小时取消报名\n'
            '5. 活动现场可能会进行拍摄，参与即表示同意使用肖像权',
            style: TextStyle(
              fontSize: 14,
              color: AppConstants.textSecondary,
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildParticipants() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingLarge),
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        border: Border.all(color: AppConstants.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                '参与者',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppConstants.textPrimary,
                ),
              ),
              const Spacer(),
              Text(
                '${widget.activity.participants}人已参与',
                style: const TextStyle(
                  fontSize: 14,
                  color: AppConstants.textSecondary,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // 参与者头像列表
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: List.generate(
              widget.activity.participants > 20 ? 20 : widget.activity.participants,
              (index) => CircleAvatar(
                radius: 20,
                backgroundImage: AssetImage('assets/images/avatar/avatar${(index % 5) + 1}.jpg'),
              ),
            ),
          ),
          
          if (widget.activity.participants > 20) ...[
            const SizedBox(height: 8),
            Text(
              '还有${widget.activity.participants - 20}人已参与...',
              style: const TextStyle(
                fontSize: 12,
                color: AppConstants.textSecondary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBottomBar() {
    final isActive = widget.activity.status == 'active';
    final isFull = widget.activity.participants >= widget.activity.maxParticipants;
    
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 收藏按钮
            OutlinedButton.icon(
              onPressed: _toggleFavorite,
              icon: const Icon(Icons.favorite_border),
              label: const Text('收藏'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppConstants.primaryColor,
                side: const BorderSide(color: AppConstants.primaryColor),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
            ),
            
            const SizedBox(width: 16),
            
            // 参与/取消按钮
            Expanded(
              child: ElevatedButton(
                onPressed: _isLoading ? null : (isActive && !isFull ? _toggleParticipation : null),
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isParticipating 
                      ? AppConstants.textSecondary 
                      : AppConstants.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        _getButtonText(isActive, isFull),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (widget.activity.status) {
      case 'active':
        return Colors.green;
      case 'ended':
        return Colors.grey;
      case 'cancelled':
        return Colors.red;
      default:
        return AppConstants.primaryColor;
    }
  }

  String _getStatusText() {
    switch (widget.activity.status) {
      case 'active':
        return '进行中';
      case 'ended':
        return '已结束';
      case 'cancelled':
        return '已取消';
      default:
        return '未知';
    }
  }

  String _getButtonText(bool isActive, bool isFull) {
    if (!isActive) {
      return widget.activity.status == 'ended' ? '活动已结束' : '活动已取消';
    }
    
    if (isFull) {
      return '报名已满';
    }
    
    return _isParticipating ? '取消报名' : '立即报名';
  }

  void _toggleParticipation() async {
    setState(() {
      _isLoading = true;
    });

    // 模拟网络请求
    await Future.delayed(const Duration(seconds: 1));

    if (mounted) {
      setState(() {
        _isParticipating = !_isParticipating;
        _isLoading = false;
      });
      
      AppUtils.showSuccess(
        context,
        _isParticipating ? '报名成功！' : '已取消报名',
      );
      AppUtils.vibrate();
    }
  }

  void _toggleFavorite() {
    AppUtils.showSuccess(context, '收藏功能开发中...');
  }

  void _shareActivity() {
    AppUtils.showSuccess(context, '分享功能开发中...');
  }
}
