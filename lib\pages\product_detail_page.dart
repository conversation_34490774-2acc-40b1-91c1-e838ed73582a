import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/menu.dart';
import '../utils/app_utils.dart';

/// 商品详情页面 - 重新设计
class ProductDetailPage extends StatefulWidget {
  final MenuItem product;

  const ProductDetailPage({
    super.key,
    required this.product,
  });

  @override
  State<ProductDetailPage> createState() => _ProductDetailPageState();
}

class _ProductDetailPageState extends State<ProductDetailPage> {
  int _quantity = 1;
  String _selectedSize = '中杯';
  String _selectedTemperature = '热饮';
  String _selectedSweetness = '标准';
  bool _isFavorite = false;
  int _currentImageIndex = 0; // 当前图片索引
  PageController _pageController = PageController(); // 图片轮播控制器

  final List<String> _sizes = ['小杯', '中杯', '大杯', '超大杯'];
  final List<String> _temperatures = ['热饮', '温饮', '冰饮'];
  final List<String> _sweetness = ['无糖', '少糖', '标准', '多糖'];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  // 获取商品图片列表
  List<String> get _productImages {
    if (widget.product.images != null && widget.product.images!.isNotEmpty) {
      return widget.product.images!;
    }
    return [widget.product.image]; // 如果没有多图，使用单图
  }

  @override
  Widget build(BuildContext context) {
    print('=== ProductDetailPage build called ===');
    print('Product: ${widget.product.name}');
    print('Product ID: ${widget.product.id}');

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          widget.product.name ?? '商品详情', // 添加空安全检查，如果名称为null则显示“商品详情”
          style: const TextStyle(
              color: AppConstants.textPrimary,
              fontSize: 18,
              fontWeight: FontWeight.bold),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppConstants.textPrimary),
          onPressed: () {
            print('Back button pressed');
            Navigator.pop(context);
          },
        ),
        actions: [
          IconButton(
            icon: Icon(
              _isFavorite ? Icons.favorite : Icons.favorite_border,
              color: _isFavorite ? Colors.red : AppConstants.textPrimary,
            ),
            onPressed: () {
              print('Favorite button pressed');
              setState(() {
                _isFavorite = !_isFavorite;
              });
              AppUtils.showSuccess(
                context,
                _isFavorite ? '已添加到收藏' : '已取消收藏',
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProductImage(),
            _buildProductInfo(),
            const SizedBox(height: 8),
            _buildSpecificationSection(),
            _buildDescriptionSection(),
            _buildNutritionSection(),
            _buildReviewsSection(),
            const SizedBox(height: 100), // 增加底部间距，避免被底部栏遮挡
          ],
        ),
      ),
      // 底部操作栏
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  // 商品图片区域 - 支持多图横向滑动
  Widget _buildProductImage() {
    final images = _productImages;

    return Container(
      height: 300, // 增加高度
      width: double.infinity,
      child: Stack(
        children: [
          // 图片轮播
          Hero(
            tag: 'product_${widget.product.id}',
            child: Material(
              type: MaterialType.transparency,
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentImageIndex = index;
                  });
                },
                itemCount: images.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: Image.asset(
                        images[index],
                        fit: BoxFit.cover, // 撑满整个显示区域
                        width: double.infinity,
                        height: double.infinity,
                        errorBuilder: (context, error, stackTrace) => Container(
                          color: AppConstants.backgroundColor,
                          child: const Icon(
                            Icons.local_cafe,
                            size: 80,
                            color: AppConstants.textLight,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          // 图片指示器（如果有多张图片）
          if (images.length > 1)
            Positioned(
              bottom: 30,
              left: 0,
              right: 0,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: images.asMap().entries.map((entry) {
                  return Container(
                    width: 8,
                    height: 8,
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _currentImageIndex == entry.key
                          ? AppConstants.primaryColor
                          : Colors.white.withOpacity(0.5),
                    ),
                  );
                }).toList(),
              ),
            ),
        ],
      ),
    );
  }

  // 商品信息区域
  Widget _buildProductInfo() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 商品名称和热销标签
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                child: Text( // 使用 Flexible 替代 Expanded，增加布局弹性
                  widget.product.name ?? '未知商品', // 添加空安全检查，如果名称为null则显示“未知商品”
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.textPrimary,
                  ),
                ),
              ),
              if (widget.product.popular)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    '热销',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // 商品描述
          Text(
            widget.product.description ?? '暂无描述', // 添加空安全检查，如果描述为null则显示“暂无描述”
            style: const TextStyle(
              fontSize: 16,
              color: AppConstants.textSecondary,
              height: 1.5,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 价格信息
          Wrap(
            spacing: 12,
            crossAxisAlignment: WrapCrossAlignment.end,
            children: [
              Text(
                AppUtils.formatPrice(widget.product.price),
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: AppConstants.primaryColor,
                ),
              ),
              if (widget.product.originalPrice != null && 
                  widget.product.originalPrice! > widget.product.price)
                Text(
                  AppUtils.formatPrice(widget.product.originalPrice!),
                  style: const TextStyle(
                    fontSize: 16,
                    color: AppConstants.textLight,
                    decoration: TextDecoration.lineThrough,
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // 评分和销量
          Wrap(
            spacing: 16.0,
            crossAxisAlignment: WrapCrossAlignment.center,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: List.generate(5, (index) {
                  return Icon(
                    index < 4 ? Icons.star : Icons.star_border,
                    color: Colors.amber,
                    size: 20,
                  );
                }),
              ),
              Text(
                '4.8',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppConstants.textPrimary,
                ),
              ),
              Text(
                '月销 1.2k+',
                style: const TextStyle(
                  fontSize: 14,
                  color: AppConstants.textSecondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 规格选择区域
  Widget _buildSpecificationSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '规格选择',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          
          // 杯型选择
          _buildSpecOption('杯型', _sizes, _selectedSize, (value) {
            setState(() {
              _selectedSize = value;
            });
          }),
          
          const SizedBox(height: 16),
          
          // 温度选择
          if ((widget.product.hot ?? false) || (widget.product.iced ?? false)) 
            Column(
              children: [
                _buildSpecOption('温度', _temperatures, _selectedTemperature, (value) {
                  setState(() { _selectedTemperature = value; });
                }),
                const SizedBox(height: 16),
              ],
            ),
          
          // 甜度选择
          _buildSpecOption('甜度', _sweetness, _selectedSweetness, (value) {
            setState(() {
              _selectedSweetness = value;
            });
          }),
        ],
      ),
    );
  }

  // 规格选项构建器
  Widget _buildSpecOption(String title, List<String> options, String selected, Function(String) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppConstants.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8, // 增加换行后的上下间距
          children: options.map((option) {
            final isSelected = option == selected;
            return GestureDetector(
              onTap: () => onChanged(option),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: isSelected ? AppConstants.primaryColor : Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected ? AppConstants.primaryColor : Colors.grey.shade300,
                  ),
                ),
                child: Text(
                  option,
                  style: TextStyle(
                    color: isSelected ? Colors.white : AppConstants.textPrimary,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  // 商品描述区域
  Widget _buildDescriptionSection() {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '商品详情',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimary,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              '精选优质咖啡豆，经过专业烘焙师精心调配，口感醇厚，香气浓郁。每一杯都是对品质的坚持，为您带来完美的咖啡体验。适合在任何时间享用，无论是清晨的第一杯咖啡，还是午后的小憩时光。',
              style: TextStyle(
                fontSize: 15,
                color: AppConstants.textSecondary,
                height: 1.6,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 营养信息区域
  Widget _buildNutritionSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '营养信息',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppConstants.textPrimary,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                _buildNutritionItem('热量', '180 kcal'),
                _buildNutritionItem('蛋白质', '8.5g'),
                _buildNutritionItem('碳水化合物', '15.2g'),
                _buildNutritionItem('脂肪', '9.8g'),
                _buildNutritionItem('咖啡因', '95mg'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionItem(String name, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Text(
              name,
              style: const TextStyle(
                fontSize: 15,
                color: AppConstants.textSecondary,
              ),
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  // 用户评价区域
  Widget _buildReviewsSection() {
    return Container(
      margin: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Flexible(
                child: Text(
                  '用户评价',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.textPrimary,
                  ),
                ),
              ),
              TextButton(
                onPressed: () {
                  // 查看更多评价
                },
                child: const Text(
                  '查看全部',
                  style: TextStyle(
                    color: AppConstants.primaryColor,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // 评价列表
          ...List.generate(3, (index) => _buildReviewItem(index)),
        ],
      ),
    );
  }

  Widget _buildReviewItem(int index) {
    final reviews = [
      {
        'name': '张小明',
        'avatar': 'assets/images/avatar/avatar1.jpg',
        'rating': 5,
        'time': '2024-01-15',
        'content': '味道很棒，香气浓郁，口感顺滑。服务也很好，会再次购买！',
      },
      {
        'name': 'Sarah',
        'avatar': 'assets/images/avatar/avatar2.jpg',
        'rating': 4,
        'time': '2024-01-14',
        'content': 'Great coffee! Perfect for morning boost. Love the rich flavor.',
      },
      {
        'name': '李华',
        'avatar': 'assets/images/avatar/avatar3.jpg',
        'rating': 5,
        'time': '2024-01-13',
        'content': '经常买这款咖啡，品质稳定，价格合理，推荐给大家。',
      },
    ];

    final review = reviews[index];

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: Colors.grey.shade200,
                child: ClipOval(
                  child: Image.asset(
                    review['avatar'] as String,
                    fit: BoxFit.cover,
                    width: 40,
                    height: 40,
                    errorBuilder: (context, error, stackTrace) {
                      // 如果图片加载失败，显示一个备用图标，防止应用崩溃
                      return const Icon(
                        Icons.person_outline,
                        color: Colors.grey,
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      review['name'] as String,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.textPrimary,
                      ),
                    ),
                    Row(
                      children: [
                        Row(
                          children: List.generate(5, (starIndex) {
                            return Icon(
                              starIndex < (review['rating'] as int)
                                  ? Icons.star
                                  : Icons.star_border,
                              color: Colors.amber,
                              size: 16,
                            );
                          }),
                        ),
                        const SizedBox(width: 8),
                        Flexible( // 添加 Flexible 确保时间文本不会溢出
                          child: Text(
                            review['time'] as String,
                            style: const TextStyle(
                              fontSize: 12,
                              color: AppConstants.textLight,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            review['content'] as String,
            style: const TextStyle(
              fontSize: 14,
              color: AppConstants.textSecondary,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  // 底部操作栏
  Widget _buildBottomBar() {
    return Container(
      height: 100, // 限制底部操作栏的高度
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 数量选择器
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed: _quantity > 1 ? () {
                      setState(() {
                        _quantity--;
                      });
                    } : null,
                    icon: const Icon(Icons.remove),
                    color: _quantity > 1 ? AppConstants.primaryColor : Colors.grey, // 数量为1时禁用颜色
                  ),
                  Container(
                    width: 40,
                    alignment: Alignment.center,
                    child: Text(
                      _quantity.toString(),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppConstants.textPrimary,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _quantity++;
                      });
                    },
                    icon: const Icon(Icons.add),
                    color: AppConstants.primaryColor,
                  ),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // 加入购物车按钮
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  _addToCart();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                  elevation: 0,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Flexible(
                      child: Text(
                        '加入购物车 ${AppUtils.formatPrice(widget.product.price * _quantity)}',
                        overflow: TextOverflow.ellipsis,
                        softWrap: false,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addToCart() {
    // 添加到购物车逻辑
    AppUtils.showSuccess(
      context,
      '已添加 $_quantity 杯 ${widget.product.name} 到购物车',
    );
    AppUtils.vibrate();

    // 可以在这里添加实际的购物车逻辑
    // CartService.addItem(widget.product, _quantity, specifications);
  }
}
