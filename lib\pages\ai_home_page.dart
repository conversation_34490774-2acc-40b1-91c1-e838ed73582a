import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import 'ai_assistant_page.dart';

/// AI助手主页 - 小盛
class AIHomePage extends StatelessWidget {
  const AIHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: AppConstants.primaryColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Icon(
                Icons.smart_toy,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 8),
            const Text('小盛'),
          ],
        ),
        automaticallyImplyLeading: false,
        elevation: 0,
        backgroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 欢迎区域
            _buildWelcomeSection(context),
            
            const SizedBox(height: AppConstants.paddingLarge),
            
            // 功能卡片区域
            _buildFunctionCards(context),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.primaryColor.withValues(alpha: 0.1),
            AppConstants.primaryColor.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        border: Border.all(
          color: AppConstants.primaryColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // AI头像
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: AppConstants.primaryColor,
              borderRadius: BorderRadius.circular(40),
              boxShadow: [
                BoxShadow(
                  color: AppConstants.primaryColor.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: const Icon(
              Icons.smart_toy,
              color: Colors.white,
              size: 40,
            ),
          ),
          
          const SizedBox(height: AppConstants.paddingMedium),
          
          // 欢迎语
          const Text(
            '你好我是小盛，请问有什么可以帮到您',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppConstants.textPrimary,
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: AppConstants.paddingSmall),
          
          Text(
            '我是新易盛咖啡馆的AI助手，随时为您服务',
            style: TextStyle(
              fontSize: 14,
              color: AppConstants.textSecondary,
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionCards(BuildContext context) {
    final functions = [
      {
        'title': '点我聊天',
        'subtitle': '与小盛自由对话',
        'icon': Icons.chat_bubble_outline,
        'color': AppConstants.primaryColor,
        'onTap': () => _navigateToChat(context),
      },
      {
        'title': '饮品推荐',
        'subtitle': '为您推荐人气饮品',
        'icon': Icons.local_cafe_outlined,
        'color': Colors.orange,
        'onTap': () => _navigateToChatWithMessage(context, '请为我推荐一些饮品'),
      },
      {
        'title': '最近热点',
        'subtitle': '了解社区热门话题',
        'icon': Icons.trending_up_outlined,
        'color': Colors.red,
        'onTap': () => _navigateToChatWithMessage(context, '最近有什么热点话题'),
      },
      {
        'title': '活动咨询',
        'subtitle': '查询最新优惠活动',
        'icon': Icons.local_activity_outlined,
        'color': Colors.green,
        'onTap': () => _navigateToChatWithMessage(context, '有什么优惠活动'),
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '我能为您做什么',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppConstants.textPrimary,
          ),
        ),
        
        const SizedBox(height: AppConstants.paddingMedium),
        
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: AppConstants.paddingSmall,
            mainAxisSpacing: AppConstants.paddingSmall,
            childAspectRatio: 1.2,
          ),
          itemCount: functions.length,
          itemBuilder: (context, index) {
            final function = functions[index];
            return _buildFunctionCard(
              title: function['title'] as String,
              subtitle: function['subtitle'] as String,
              icon: function['icon'] as IconData,
              color: function['color'] as Color,
              onTap: function['onTap'] as VoidCallback,
            );
          },
        ),
      ],
    );
  }

  Widget _buildFunctionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 44,
              height: 44,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                icon,
                color: color,
                size: 22,
              ),
            ),
            
            const SizedBox(height: 8),
            
            Text(
              title,
              style: const TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w600,
                color: AppConstants.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 2),
            
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 11,
                color: AppConstants.textSecondary,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }



  void _navigateToChat(BuildContext context) {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => const AIAssistantPage(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.ease;
          var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
      ),
    );
  }

  void _navigateToChatWithMessage(BuildContext context, String message) {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => AIAssistantPage(initialMessage: message),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.ease;
          var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
      ),
    );
  }
}
