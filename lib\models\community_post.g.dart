// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'community_post.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CommunityPost _$CommunityPostFromJson(Map<String, dynamic> json) =>
    CommunityPost(
      id: (json['id'] as num).toInt(),
      userId: (json['userId'] as num).toInt(),
      content: json['content'] as String,
      images:
          (json['images'] as List<dynamic>).map((e) => e as String).toList(),
      timestamp: json['timestamp'] as String,
      likes: (json['likes'] as num).toInt(),
      comments: (json['comments'] as num).toInt(),
      shares: (json['shares'] as num).toInt(),
      liked: json['liked'] as bool,
      location: json['location'] as String?,
    );

Map<String, dynamic> _$CommunityPostToJson(CommunityPost instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'content': instance.content,
      'images': instance.images,
      'timestamp': instance.timestamp,
      'likes': instance.likes,
      'comments': instance.comments,
      'shares': instance.shares,
      'liked': instance.liked,
      'location': instance.location,
    };

Comment _$CommentFromJson(Map<String, dynamic> json) => Comment(
      id: (json['id'] as num).toInt(),
      userId: (json['userId'] as num).toInt(),
      content: json['content'] as String,
      timestamp: json['timestamp'] as String,
      likes: (json['likes'] as num).toInt(),
    );

Map<String, dynamic> _$CommentToJson(Comment instance) => <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'content': instance.content,
      'timestamp': instance.timestamp,
      'likes': instance.likes,
    };
