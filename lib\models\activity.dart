import 'package:json_annotation/json_annotation.dart';

part 'activity.g.dart';

/// 活动模型
@JsonSerializable()
class Activity {
  final int id;
  final String title;
  final String description;
  final String date;
  final String time;
  final String location;
  final int participants;
  final int maxParticipants;
  final String image;
  final String status;

  const Activity({
    required this.id,
    required this.title,
    required this.description,
    required this.date,
    required this.time,
    required this.location,
    required this.participants,
    required this.maxParticipants,
    required this.image,
    required this.status,
  });

  factory Activity.fromJson(Map<String, dynamic> json) => _$ActivityFromJson(json);
  Map<String, dynamic> toJson() => _$ActivityToJson(this);

  Activity copyWith({
    int? id,
    String? title,
    String? description,
    String? date,
    String? time,
    String? location,
    int? participants,
    int? maxParticipants,
    String? image,
    String? status,
  }) {
    return Activity(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      date: date ?? this.date,
      time: time ?? this.time,
      location: location ?? this.location,
      participants: participants ?? this.participants,
      maxParticipants: maxParticipants ?? this.maxParticipants,
      image: image ?? this.image,
      status: status ?? this.status,
    );
  }

  /// 是否还有名额
  bool get hasAvailableSlots => participants < maxParticipants;

  /// 剩余名额
  int get availableSlots => maxParticipants - participants;

  /// 报名进度百分比
  double get participationPercentage => participants / maxParticipants;

  /// 是否即将开始
  bool get isUpcoming => status == 'upcoming';

  /// 是否正在进行
  bool get isOngoing => status == 'ongoing';

  /// 是否已结束
  bool get isCompleted => status == 'completed';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Activity && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Activity(id: $id, title: $title, date: $date, participants: $participants/$maxParticipants)';
  }
}
