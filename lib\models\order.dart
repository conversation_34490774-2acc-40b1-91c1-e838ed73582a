import 'package:json_annotation/json_annotation.dart';
import '../constants/app_constants.dart';

part 'order.g.dart';

/// 订单模型
@JsonSerializable()
class Order {
  final String id;
  final String status;
  final String statusText;
  final List<OrderItem> items;
  final double totalAmount;
  final String orderTime;
  final String? pickupTime;
  final String? estimatedTime;
  final String? cancelTime;
  final String pickupLocation;

  const Order({
    required this.id,
    required this.status,
    required this.statusText,
    required this.items,
    required this.totalAmount,
    required this.orderTime,
    this.pickupTime,
    this.estimatedTime,
    this.cancelTime,
    required this.pickupLocation,
  });

  factory Order.fromJson(Map<String, dynamic> json) => _$OrderFromJson(json);
  Map<String, dynamic> toJson() => _$OrderToJson(this);

  Order copyWith({
    String? id,
    String? status,
    String? statusText,
    List<OrderItem>? items,
    double? totalAmount,
    String? orderTime,
    String? pickupTime,
    String? estimatedTime,
    String? cancelTime,
    String? pickupLocation,
  }) {
    return Order(
      id: id ?? this.id,
      status: status ?? this.status,
      statusText: statusText ?? this.statusText,
      items: items ?? this.items,
      totalAmount: totalAmount ?? this.totalAmount,
      orderTime: orderTime ?? this.orderTime,
      pickupTime: pickupTime ?? this.pickupTime,
      estimatedTime: estimatedTime ?? this.estimatedTime,
      cancelTime: cancelTime ?? this.cancelTime,
      pickupLocation: pickupLocation ?? this.pickupLocation,
    );
  }

  /// 是否可以取消
  bool get canCancel {
    return status == AppConstants.orderStatusPending || 
           status == AppConstants.orderStatusPaid;
  }

  /// 是否已完成
  bool get isCompleted {
    return status == AppConstants.orderStatusCompleted;
  }

  /// 是否已取消
  bool get isCancelled {
    return status == AppConstants.orderStatusCancelled;
  }

  /// 是否正在制作
  bool get isPreparing {
    return status == AppConstants.orderStatusPreparing;
  }

  /// 是否可以取餐
  bool get isReady {
    return status == AppConstants.orderStatusReady;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Order && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Order(id: $id, status: $status, totalAmount: $totalAmount)';
  }
}

/// 订单项模型
@JsonSerializable()
class OrderItem {
  final int id;
  final String name;
  final double price;
  final int quantity;
  final List<String> options;

  const OrderItem({
    required this.id,
    required this.name,
    required this.price,
    required this.quantity,
    required this.options,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) => _$OrderItemFromJson(json);
  Map<String, dynamic> toJson() => _$OrderItemToJson(this);

  OrderItem copyWith({
    int? id,
    String? name,
    double? price,
    int? quantity,
    List<String>? options,
  }) {
    return OrderItem(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      options: options ?? this.options,
    );
  }

  /// 总价
  double get totalPrice => price * quantity;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'OrderItem(id: $id, name: $name, quantity: $quantity, price: $price)';
  }
}
