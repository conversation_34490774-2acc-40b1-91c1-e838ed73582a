import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../models/community_post.dart';
import '../services/mock_data_service.dart';
import '../utils/app_utils.dart';
import '../utils/page_transitions.dart';
import 'post_detail_page.dart';

/// 热门动态页面
class HotPostsPage extends StatefulWidget {
  const HotPostsPage({super.key});

  @override
  State<HotPostsPage> createState() => _HotPostsPageState();
}

class _HotPostsPageState extends State<HotPostsPage> {
  final MockDataService _dataService = MockDataService();
  List<CommunityPost> _hotPosts = [];
  final Map<int, bool> _likedPosts = {};
  final Map<int, int> _likeCounts = {};

  @override
  void initState() {
    super.initState();
    _loadHotPosts();
  }

  void _loadHotPosts() {
    // 获取最新20条热门动态（点赞数>10的动态）
    final allPosts = _dataService.getCommunityPosts();
    _hotPosts = allPosts
        .where((post) => post.likes > 10)
        .take(20)
        .toList();
    
    // 按点赞数排序
    _hotPosts.sort((a, b) => b.likes.compareTo(a.likes));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: CustomScrollView(
        slivers: [
          // 自定义AppBar
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            backgroundColor: AppConstants.primaryColor,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: () => Navigator.of(context).pop(),
            ),
            flexibleSpace: FlexibleSpaceBar(
              title: const Text(
                '热门动态',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppConstants.primaryColor,
                      AppConstants.primaryColor.withOpacity(0.8),
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      right: -50,
                      top: -50,
                      child: Icon(
                        Icons.trending_up,
                        size: 200,
                        color: Colors.white.withOpacity(0.1),
                      ),
                    ),
                    Positioned(
                      left: 20,
                      bottom: 80,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '发现精彩',
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.9),
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${_hotPosts.length}条热门动态',
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.7),
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          
          // 热门动态列表
          SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final post = _hotPosts[index];
                  return _buildPostCard(post, index);
                },
                childCount: _hotPosts.length,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostCard(CommunityPost post, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _navigateToPostDetail(post),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 用户信息和热门标签
              Row(
                children: [
                  // 热门排名
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: _getRankColor(index),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  
                  // 用户头像
                  CircleAvatar(
                    radius: 20,
                    backgroundImage: AssetImage(_getLocalAvatarPath(post.userId.toString())),
                  ),
                  const SizedBox(width: 12),
                  
                  // 用户信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              '用户${post.userId}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(width: 8),
                            if (index < 3) // 前三名显示热门标签
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Text(
                                  '热门',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                          ],
                        ),
                        Text(
                          post.timestamp,
                          style: TextStyle(
                            color: AppConstants.textLight,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // 动态内容
              Text(
                post.content,
                style: const TextStyle(
                  fontSize: 14,
                  height: 1.4,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              
              // 动态图片（如果有）
              if (post.images.isNotEmpty) ...[
                const SizedBox(height: 12),
                SizedBox(
                  height: 80,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: post.images.length > 3 ? 3 : post.images.length,
                    itemBuilder: (context, imgIndex) {
                      return Container(
                        width: 80,
                        height: 80,
                        margin: const EdgeInsets.only(right: 8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          image: DecorationImage(
                            image: AssetImage(_getLocalImagePath(imgIndex)),
                            fit: BoxFit.cover,
                          ),
                        ),
                        child: post.images.length > 3 && imgIndex == 2
                            ? Container(
                                decoration: BoxDecoration(
                                  color: Colors.black.withOpacity(0.5),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Center(
                                  child: Text(
                                    '+${post.images.length - 2}',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              )
                            : null,
                      );
                    },
                  ),
                ),
              ],
              
              const SizedBox(height: 12),
              
              // 互动数据
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      _buildInteractionButton(
                        icon: (_likedPosts[post.id] ?? post.liked) 
                            ? Icons.favorite 
                            : Icons.favorite_border,
                        count: _likeCounts[post.id] ?? post.likes,
                        color: (_likedPosts[post.id] ?? post.liked) 
                            ? AppConstants.errorColor 
                            : AppConstants.textLight,
                        onTap: () => _toggleLike(post),
                      ),
                      const SizedBox(width: 16),
                      _buildInteractionButton(
                        icon: Icons.chat_bubble_outline,
                        count: post.comments,
                        color: AppConstants.textLight,
                        onTap: () => _navigateToPostDetail(post),
                      ),
                      const SizedBox(width: 16),
                      _buildInteractionButton(
                        icon: Icons.share_outlined,
                        count: post.shares,
                        color: AppConstants.textLight,
                        onTap: () => _sharePost(post),
                      ),
                    ],
                  ),
                  
                  // 箭头图标
                  const Icon(
                    Icons.arrow_forward_ios,
                    color: AppConstants.textLight,
                    size: 16,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInteractionButton({
    required IconData icon,
    required int count,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Color _getRankColor(int index) {
    switch (index) {
      case 0:
        return Colors.amber; // 金色
      case 1:
        return Colors.grey.shade400; // 银色
      case 2:
        return Colors.brown; // 铜色
      default:
        return AppConstants.primaryColor;
    }
  }

  String _getLocalAvatarPath(String userId) {
    // 根据用户ID返回本地头像路径
    final avatarIndex = (int.tryParse(userId) ?? 1) % 4 + 1;
    return 'assets/images/avatar/avatar$avatarIndex.jpg';
  }

  String _getLocalImagePath(int index) {
    // 根据索引返回本地社区图片路径
    final imageIndex = (index % 4) + 1;
    return 'assets/images/community/community$imageIndex.jpg';
  }

  void _navigateToPostDetail(CommunityPost post) {
    context.pushSlideFromRight(PostDetailPage(post: post));
  }

  void _toggleLike(CommunityPost post) {
    setState(() {
      final currentLiked = _likedPosts[post.id] ?? post.liked;
      final currentCount = _likeCounts[post.id] ?? post.likes;

      _likedPosts[post.id] = !currentLiked;
      _likeCounts[post.id] = currentLiked ? currentCount - 1 : currentCount + 1;
    });
    AppUtils.vibrate();
    AppUtils.showToast(context, _likedPosts[post.id]! ? '已点赞' : '已取消点赞');
  }

  void _sharePost(CommunityPost post) {
    AppUtils.showToast(context, '分享功能开发中...');
  }
}
