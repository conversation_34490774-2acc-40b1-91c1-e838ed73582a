import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

/// 用户模型
@JsonSerializable()
class User {
  final int id;
  final String name;
  final String avatar;
  final String position;
  final String location;
  final bool verified;
  final String? employeeId;
  final String? joinDate;
  final int? points;
  final String? level;

  const User({
    required this.id,
    required this.name,
    required this.avatar,
    required this.position,
    required this.location,
    required this.verified,
    this.employeeId,
    this.joinDate,
    this.points,
    this.level,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    int? id,
    String? name,
    String? avatar,
    String? position,
    String? location,
    bool? verified,
    String? employeeId,
    String? joinDate,
    int? points,
    String? level,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      avatar: avatar ?? this.avatar,
      position: position ?? this.position,
      location: location ?? this.location,
      verified: verified ?? this.verified,
      employeeId: employeeId ?? this.employeeId,
      joinDate: joinDate ?? this.joinDate,
      points: points ?? this.points,
      level: level ?? this.level,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
