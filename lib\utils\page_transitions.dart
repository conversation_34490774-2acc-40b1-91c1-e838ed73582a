import 'package:flutter/material.dart';

/// 页面转场动画工具类
class PageTransitions {
  /// 从右向左滑入的页面路由
  static PageRouteBuilder<T> slideFromRight<T extends Object?>(
    Widget page, {
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // 主页面动画：从右向左滑入
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        final slideTween = Tween(begin: begin, end: end);
        final slideAnimation = animation.drive(slideTween.chain(CurveTween(curve: curve)));

        // 背景页面动画：向左滑出一点点
        const secondaryBegin = Offset.zero;
        const secondaryEnd = Offset(-0.3, 0.0);
        final secondarySlideTween = Tween(begin: secondaryBegin, end: secondaryEnd);
        final secondarySlideAnimation = secondaryAnimation.drive(
          secondarySlideTween.chain(CurveTween(curve: curve)),
        );

        return Stack(
          children: [
            // 背景页面
            SlideTransition(
              position: secondarySlideAnimation,
              child: Container(
                color: Colors.black.withValues(alpha: 0.1),
              ),
            ),
            // 主页面
            SlideTransition(
              position: slideAnimation,
              child: child,
            ),
          ],
        );
      },
    );
  }

  /// 从下向上滑入的页面路由
  static PageRouteBuilder<T> slideFromBottom<T extends Object?>(
    Widget page, {
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;
        final tween = Tween(begin: begin, end: end);
        final offsetAnimation = animation.drive(tween.chain(CurveTween(curve: curve)));

        return SlideTransition(
          position: offsetAnimation,
          child: child,
        );
      },
    );
  }

  /// 淡入淡出的页面路由
  static PageRouteBuilder<T> fadeIn<T extends Object?>(
    Widget page, {
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation.drive(CurveTween(curve: curve)),
          child: child,
        );
      },
    );
  }

  /// 缩放动画的页面路由
  static PageRouteBuilder<T> scaleIn<T extends Object?>(
    Widget page, {
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return ScaleTransition(
          scale: animation.drive(
            CurveTween(curve: curve).chain(
              Tween(begin: 0.8, end: 1.0),
            ),
          ),
          child: FadeTransition(
            opacity: animation.drive(CurveTween(curve: curve)),
            child: child,
          ),
        );
      },
    );
  }

  /// 旋转淡入的页面路由
  static PageRouteBuilder<T> rotateIn<T extends Object?>(
    Widget page, {
    Duration duration = const Duration(milliseconds: 400),
    Curve curve = Curves.easeInOut,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return RotationTransition(
          turns: animation.drive(
            CurveTween(curve: curve).chain(
              Tween(begin: 0.1, end: 0.0),
            ),
          ),
          child: ScaleTransition(
            scale: animation.drive(
              CurveTween(curve: curve).chain(
                Tween(begin: 0.8, end: 1.0),
              ),
            ),
            child: FadeTransition(
              opacity: animation.drive(CurveTween(curve: curve)),
              child: child,
            ),
          ),
        );
      },
    );
  }

  /// 自定义滑动方向的页面路由
  static PageRouteBuilder<T> slideFromDirection<T extends Object?>(
    Widget page,
    Offset direction, {
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        final tween = Tween(begin: direction, end: Offset.zero);
        final offsetAnimation = animation.drive(tween.chain(CurveTween(curve: curve)));

        return SlideTransition(
          position: offsetAnimation,
          child: child,
        );
      },
    );
  }

  /// 组合动画：滑动 + 淡入 + 缩放
  static PageRouteBuilder<T> combinedTransition<T extends Object?>(
    Widget page, {
    Duration duration = const Duration(milliseconds: 350),
    Curve curve = Curves.easeInOut,
    Offset slideDirection = const Offset(1.0, 0.0),
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // 滑动动画
        final slideTween = Tween(begin: slideDirection, end: Offset.zero);
        final slideAnimation = animation.drive(slideTween.chain(CurveTween(curve: curve)));

        // 缩放动画
        final scaleTween = Tween(begin: 0.9, end: 1.0);
        final scaleAnimation = animation.drive(
          CurveTween(curve: curve).chain(scaleTween),
        );

        // 透明度动画
        final opacityAnimation = animation.drive(CurveTween(curve: curve));

        return SlideTransition(
          position: slideAnimation,
          child: ScaleTransition(
            scale: scaleAnimation,
            child: FadeTransition(
              opacity: opacityAnimation,
              child: child,
            ),
          ),
        );
      },
    );
  }
}

/// 页面转场动画扩展方法
extension NavigatorExtensions on NavigatorState {
  /// 使用右向左滑入动画推入页面
  Future<T?> pushSlideFromRight<T extends Object?>(Widget page) {
    return push<T>(PageTransitions.slideFromRight<T>(page));
  }

  /// 使用下向上滑入动画推入页面
  Future<T?> pushSlideFromBottom<T extends Object?>(Widget page) {
    return push<T>(PageTransitions.slideFromBottom<T>(page));
  }

  /// 使用淡入动画推入页面
  Future<T?> pushFadeIn<T extends Object?>(Widget page) {
    return push<T>(PageTransitions.fadeIn<T>(page));
  }

  /// 使用缩放动画推入页面
  Future<T?> pushScaleIn<T extends Object?>(Widget page) {
    return push<T>(PageTransitions.scaleIn<T>(page));
  }

  /// 使用组合动画推入页面
  Future<T?> pushCombined<T extends Object?>(Widget page) {
    return push<T>(PageTransitions.combinedTransition<T>(page));
  }
}

/// BuildContext 扩展方法
extension BuildContextExtensions on BuildContext {
  /// 使用右向左滑入动画推入页面
  Future<T?> pushSlideFromRight<T extends Object?>(Widget page) {
    return Navigator.of(this).pushSlideFromRight<T>(page);
  }

  /// 使用下向上滑入动画推入页面
  Future<T?> pushSlideFromBottom<T extends Object?>(Widget page) {
    return Navigator.of(this).pushSlideFromBottom<T>(page);
  }

  /// 使用淡入动画推入页面
  Future<T?> pushFadeIn<T extends Object?>(Widget page) {
    return Navigator.of(this).pushFadeIn<T>(page);
  }

  /// 使用缩放动画推入页面
  Future<T?> pushScaleIn<T extends Object?>(Widget page) {
    return Navigator.of(this).pushScaleIn<T>(page);
  }

  /// 使用组合动画推入页面
  Future<T?> pushCombined<T extends Object?>(Widget page) {
    return Navigator.of(this).pushCombined<T>(page);
  }
}
