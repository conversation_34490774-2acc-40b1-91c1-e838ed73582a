name: xys_coffee_app
description: 新易盛咖啡馆Flutter移动端应用

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI组件
  cupertino_icons: ^1.0.2
  
  # 状态管理
  provider: ^6.0.5
  
  # 网络请求
  dio: ^5.3.2
  
  # 本地存储
  shared_preferences: ^2.2.2
  
  # 图片加载
  cached_network_image: ^3.3.0
  
  # 轮播图
  carousel_slider: ^4.2.1
  
  # 下拉刷新
  pull_to_refresh: ^2.0.0
  
  # 图片选择
  image_picker: ^1.0.4
  
  # 权限管理
  permission_handler: ^11.0.1
  
  # 路由管理
  go_router: ^12.1.1
  
  # 时间格式化
  intl: ^0.18.1
  
  # JSON序列化
  json_annotation: ^4.8.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  flutter_lints: ^3.0.0
  json_serializable: ^6.7.1
  build_runner: ^2.4.7

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/images/productCoffee/
    - assets/images/productTea/
    - assets/images/productDessert/
    - assets/images/productSnack/
    - assets/images/avatar/
    - assets/images/activity/
    - assets/images/community/
    - assets/images/defaultAvatar/
    - assets/images/defaultProduct/
    - assets/menu/
    - assets/banners/
    - assets/icons/
    - assets/avatars/
  
  # fonts:
  #   - family: PingFang
  #     fonts:
  #       - asset: assets/fonts/PingFang-Regular.ttf
  #       - asset: assets/fonts/PingFang-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/PingFang-Bold.ttf
  #         weight: 700
