import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../utils/app_utils.dart';

/// 优惠券页面
class CouponsPage extends StatefulWidget {
  const CouponsPage({super.key});

  @override
  State<CouponsPage> createState() => _CouponsPageState();
}

class _CouponsPageState extends State<CouponsPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<Coupon> _validCoupons = [];
  List<Coupon> _expiredCoupons = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadCoupons();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadCoupons() {
    final now = DateTime.now();
    
    setState(() {
      _validCoupons = [
        Coupon(
          id: 1,
          title: '新用户专享',
          discount: '满50减10',
          description: '仅限首次下单使用',
          minAmount: 50.0,
          discountAmount: 10.0,
          expiryDate: now.add(const Duration(days: 30)),
          type: CouponType.discount,
        ),
        Coupon(
          id: 2,
          title: '周末特惠',
          discount: '8.5折',
          description: '周末专享折扣券',
          minAmount: 30.0,
          discountPercent: 0.85,
          expiryDate: now.add(const Duration(days: 7)),
          type: CouponType.percentage,
        ),
        Coupon(
          id: 3,
          title: '免费配送',
          discount: '免配送费',
          description: '满30元免配送费',
          minAmount: 30.0,
          expiryDate: now.add(const Duration(days: 15)),
          type: CouponType.freeShipping,
        ),
      ];
      
      _expiredCoupons = [
        Coupon(
          id: 4,
          title: '双11特惠',
          discount: '满100减20',
          description: '双11活动专用券',
          minAmount: 100.0,
          discountAmount: 20.0,
          expiryDate: now.subtract(const Duration(days: 10)),
          type: CouponType.discount,
        ),
        Coupon(
          id: 5,
          title: '生日特惠',
          discount: '7折',
          description: '生日月专享优惠',
          minAmount: 50.0,
          discountPercent: 0.7,
          expiryDate: now.subtract(const Duration(days: 5)),
          type: CouponType.percentage,
        ),
      ];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text('我的优惠券'),
        backgroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppConstants.primaryColor,
          unselectedLabelColor: AppConstants.textSecondary,
          indicatorColor: AppConstants.primaryColor,
          tabs: [
            Tab(text: '未过期 (${_validCoupons.length})'),
            Tab(text: '已过期 (${_expiredCoupons.length})'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildCouponList(_validCoupons, false),
          _buildCouponList(_expiredCoupons, true),
        ],
      ),
    );
  }

  Widget _buildCouponList(List<Coupon> coupons, bool isExpired) {
    if (coupons.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isExpired ? Icons.history : Icons.card_giftcard_outlined,
              size: 80,
              color: AppConstants.textLight,
            ),
            const SizedBox(height: 16),
            Text(
              isExpired ? '暂无过期优惠券' : '暂无可用优惠券',
              style: const TextStyle(
                fontSize: 16,
                color: AppConstants.textLight,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isExpired ? '过期的优惠券会显示在这里' : '快去获取更多优惠券吧！',
              style: const TextStyle(
                fontSize: 14,
                color: AppConstants.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _loadCoupons();
      },
      child: ListView.builder(
        padding: const EdgeInsets.only(
          top: AppConstants.paddingLarge,
          bottom: AppConstants.paddingLarge,
          left: AppConstants.paddingSmall,
          right: AppConstants.paddingSmall
        ),
        itemCount: coupons.length,
        itemBuilder: (context, index) {
          final coupon = coupons[index];
          return _buildCouponItem(coupon, isExpired);
        },
      ),
    );
  }

  Widget _buildCouponItem(Coupon coupon, bool isExpired) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: isExpired ? null : () => _useCoupon(coupon),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 优惠券标题和状态
              Row(
                children: [
                  Expanded(
                    child: Text(
                      coupon.title,
                      style: TextStyle(
                        fontSize: AppConstants.fontSizeLarge,
                        fontWeight: FontWeight.w600,
                        color: isExpired
                            ? AppConstants.textLight
                            : AppConstants.textPrimary,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getCouponTypeColor(coupon.type, isExpired).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getCouponTypeText(coupon.type),
                      style: TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                        color: _getCouponTypeColor(coupon.type, isExpired),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              // 优惠券描述
              Text(
                coupon.description,
                style: TextStyle(
                  fontSize: AppConstants.fontSizeLarge,
                  color: isExpired
                      ? AppConstants.textLight
                      : AppConstants.textPrimary,
                  height: 1.4,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              // 优惠信息展示区域
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppConstants.paddingMedium),
                decoration: BoxDecoration(
                  color: isExpired
                      ? AppConstants.backgroundColor
                      : AppConstants.primaryColor.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
                ),
                child: Row(
                  children: [
                    Text(
                      coupon.discount,
                      style: TextStyle(
                        fontSize: AppConstants.fontSizeXLarge,
                        fontWeight: FontWeight.bold,
                        color: isExpired
                            ? AppConstants.textLight
                            : AppConstants.primaryColor,
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingMedium),
                    Expanded(
                      child: Text(
                        '满¥${coupon.minAmount.toStringAsFixed(0)}可用',
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeMedium,
                          color: isExpired
                              ? AppConstants.textLight
                              : AppConstants.textSecondary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              // 有效期
              Row(
                children: [
                  Expanded(
                    child: Text(
                      '有效期至：${coupon.expiryDate.year}-${coupon.expiryDate.month.toString().padLeft(2, '0')}-${coupon.expiryDate.day.toString().padLeft(2, '0')}',
                      style: TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                        color: isExpired
                            ? AppConstants.textLight
                            : AppConstants.textSecondary,
                      ),
                    ),
                  ),
                  if (isExpired)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppConstants.textLight.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        '已过期',
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: AppConstants.textLight,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              // 互动按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildInteractionButton(
                    icon: Icons.info_outline,
                    count: 0,
                    label: '详情',
                    color: AppConstants.textLight,
                    onTap: () => _showCouponDetail(coupon),
                  ),
                  if (!isExpired) ...[
                    _buildInteractionButton(
                      icon: Icons.local_offer_outlined,
                      count: 0,
                      label: '立即使用',
                      color: AppConstants.primaryColor,
                      onTap: () => _useCoupon(coupon),
                    ),
                  ],
                  _buildInteractionButton(
                    icon: Icons.share_outlined,
                    count: 0,
                    label: '分享',
                    color: AppConstants.textLight,
                    onTap: () => _shareCoupon(coupon),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getCouponTypeColor(CouponType type, bool isExpired) {
    if (isExpired) return AppConstants.textLight;
    
    switch (type) {
      case CouponType.discount:
        return Colors.red;
      case CouponType.percentage:
        return Colors.orange;
      case CouponType.freeShipping:
        return Colors.green;
    }
  }

  String _getCouponTypeText(CouponType type) {
    switch (type) {
      case CouponType.discount:
        return '满减券';
      case CouponType.percentage:
        return '折扣券';
      case CouponType.freeShipping:
        return '包邮券';
    }
  }

  void _useCoupon(Coupon coupon) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('使用优惠券'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('优惠券：${coupon.title}'),
            const SizedBox(height: 8),
            Text('优惠：${coupon.discount}'),
            const SizedBox(height: 8),
            Text('使用条件：满¥${coupon.minAmount.toStringAsFixed(0)}可用'),
            const SizedBox(height: 16),
            const Text('确定要使用这张优惠券吗？'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              AppUtils.showToast(context, '优惠券使用成功！');
            },
            child: const Text('确定使用'),
          ),
        ],
      ),
    );
  }

  Widget _buildInteractionButton({
    required IconData icon,
    required int count,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingSmall,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 20, color: color),
            const SizedBox(width: 4),
            Text(
              count > 0 ? count.toString() : label,
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCouponDetail(Coupon coupon) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(coupon.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('优惠：${coupon.discount}'),
            const SizedBox(height: 8),
            Text('描述：${coupon.description}'),
            const SizedBox(height: 8),
            Text('使用条件：满¥${coupon.minAmount.toStringAsFixed(0)}可用'),
            const SizedBox(height: 8),
            Text('有效期至：${coupon.expiryDate.year}-${coupon.expiryDate.month.toString().padLeft(2, '0')}-${coupon.expiryDate.day.toString().padLeft(2, '0')}'),
            const SizedBox(height: 8),
            Text('类型：${_getCouponTypeText(coupon.type)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _shareCoupon(Coupon coupon) {
    AppUtils.showToast(context, '分享优惠券：${coupon.title}');
  }
}

/// 优惠券模型
class Coupon {
  final int id;
  final String title;
  final String discount;
  final String description;
  final double minAmount;
  final double? discountAmount;
  final double? discountPercent;
  final DateTime expiryDate;
  final CouponType type;

  Coupon({
    required this.id,
    required this.title,
    required this.discount,
    required this.description,
    required this.minAmount,
    this.discountAmount,
    this.discountPercent,
    required this.expiryDate,
    required this.type,
  });
}

/// 优惠券类型
enum CouponType {
  discount,    // 满减券
  percentage,  // 折扣券
  freeShipping, // 包邮券
}
