import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../constants/app_constants.dart';
import '../services/mock_data_service.dart';
import '../services/app_service.dart';
import '../models/menu.dart';
import '../models/community_post.dart';
import '../models/activity.dart';
import '../utils/date_utils.dart' as app_date;
import '../utils/app_utils.dart';
import '../utils/page_transitions.dart';
import 'ai_assistant_page.dart';
import 'product_detail_page.dart';
import 'post_detail_page.dart';
import 'recommendation_page.dart';
import 'activity_detail_page.dart';

/// 首页
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final MockDataService _dataService = MockDataService();
  List<MenuItem> _recommendItems = [];
  List<CommunityPost> _hotPosts = [];
  List<Activity> _upcomingActivities = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    setState(() {
      _recommendItems = _dataService.getRecommendedItems();
      _hotPosts = _dataService.getHotPosts().take(3).toList();
      _upcomingActivities = _dataService.getUpcomingActivities().take(2).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 顶部问候语
              _buildHeader(),

              // 轮播图
              _buildBannerSection(),

              // 今日推荐
              _buildRecommendSection(),

              // 热门动态
              _buildHotPostsSection(),

              // 即将到来的活动
              _buildUpcomingActivities(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '欢迎来到',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppConstants.textSecondary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            AppConstants.appName,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: AppConstants.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '连接全球，品味生活',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppConstants.textLight,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBannerSection() {
    final banners = [
      {
        'title': '企业文化新体验',
        'subtitle': '工作之外的第三空间',
        'buttonText': '立即点餐',
        'colors': [AppConstants.primaryColor, AppConstants.secondaryColor],
        'icon': Icons.local_cafe,
      },
      {
        'title': '美学咖啡生活',
        'subtitle': '每一杯都是精心调制',
        'buttonText': '探索菜单',
        'colors': [Colors.orange.shade400, Colors.deepOrange.shade600],
        'icon': Icons.coffee,
      },
      {
        'title': '社区分享空间',
        'subtitle': '与朋友分享美好时光',
        'buttonText': '加入社区',
        'colors': [Colors.green.shade400, Colors.teal.shade600],
        'icon': Icons.people,
      },
    ];

    return SizedBox(
      height: 200,
      child: PageView.builder(
        itemCount: banners.length,
        controller: PageController(viewportFraction: 0.9),
        itemBuilder: (context, index) {
          final banner = banners[index];
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
              gradient: LinearGradient(
                colors: banner['colors'] as List<Color>,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Stack(
              children: [
                Positioned(
                  right: -20,
                  top: -20,
                  child: Icon(
                    banner['icon'] as IconData,
                    size: 120,
                    color: Colors.white.withOpacity(0.2),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(AppConstants.paddingLarge),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        banner['title'] as String,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        banner['subtitle'] as String,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () => _handleBannerAction(index),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: (banner['colors'] as List<Color>)[0],
                        ),
                        child: Text(banner['buttonText'] as String),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _handleBannerAction(int index) {
    final appService = context.read<AppService>();
    switch (index) {
      case 0: // 企业文化新体验 - 跳转到AI聊天页面
        Navigator.of(context).push(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => const AIAssistantPage(
              initialMessage: '我想了解企业文化，请为我推荐一些饮品',
            ),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              const begin = Offset(1.0, 0.0);
              const end = Offset.zero;
              const curve = Curves.ease;
              var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
              return SlideTransition(
                position: animation.drive(tween),
                child: child,
              );
            },
          ),
        );
        break;
      case 1: // 探索菜单
        appService.setCurrentTabIndex(1);
        break;
      case 2: // 加入社区
        appService.setCurrentTabIndex(3);
        break;
    }
  }



  Widget _buildRecommendSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '今日推荐',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              TextButton(
                onPressed: () {
                  // 跳转到推荐商品页面
                  Navigator.of(context).push(
                    PageRouteBuilder(
                      pageBuilder: (context, animation, secondaryAnimation) => const RecommendationPage(),
                      transitionsBuilder: (context, animation, secondaryAnimation, child) {
                        const begin = Offset(1.0, 0.0);
                        const end = Offset.zero;
                        const curve = Curves.ease;
                        var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
                        return SlideTransition(
                          position: animation.drive(tween),
                          child: child,
                        );
                      },
                    ),
                  );
                },
                child: const Text('查看更多'),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _recommendItems.length,
              itemBuilder: (context, index) {
                final item = _recommendItems[index];
                return _buildRecommendItem(item);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendItem(MenuItem item) {
    return GestureDetector(
      onTap: () => _showProductDetail(item),
      child: Container(
        width: 160,
        margin: const EdgeInsets.only(right: AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
          // 商品图片
          ClipRRect(
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(AppConstants.radiusMedium),
            ),
            child: Image.asset(
              item.image,
              width: double.infinity,
              height: 100,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                width: double.infinity,
                height: 100,
                color: AppConstants.backgroundColor,
                child: const Icon(
                  Icons.local_cafe,
                  color: AppConstants.textLight,
                  size: 40,
                ),
              ),
            ),
          ),

          // 商品信息
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.paddingSmall),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.name,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeMedium,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    item.description,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.textLight,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const Spacer(),
                  Row(
                    children: [
                      Text(
                        AppUtils.formatPrice(item.price),
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeMedium,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.primaryColor,
                        ),
                      ),
                      if (item.hasDiscount) ...[
                        const SizedBox(width: 4),
                        Text(
                          AppUtils.formatPrice(item.originalPrice!),
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeSmall,
                            color: AppConstants.textLight,
                            decoration: TextDecoration.lineThrough,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),
          ],
        ),
      ),
    );
  }

  Widget _buildHotPostsSection() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '热门动态',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              TextButton(
                onPressed: () {
                  // 切换到社区页面并显示热门分类
                  final appService = context.read<AppService>();
                  appService.setCurrentTabIndex(3);
                  // 设置社区页面显示热门分类
                  appService.setCommunityTabIndex(1); // 1对应热门分类
                },
                child: const Text('查看更多'),
              ),
            ],
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Column(
            children: _hotPosts.map((post) => _buildPostItem(post)).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildPostItem(CommunityPost post) {
    final user = _dataService.getUserById(post.userId);
    if (user == null) return const SizedBox.shrink();

    return GestureDetector(
      onTap: () => _showPostDetail(post),
      child: Container(
        margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户信息
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundImage: AssetImage(user.avatar),
                  onBackgroundImageError: (exception, stackTrace) {},
                  child: user.avatar.isEmpty
                      ? const Icon(Icons.person, size: 20)
                      : null,
                ),
                const SizedBox(width: AppConstants.paddingSmall),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            user.name,
                            style: const TextStyle(
                              fontSize: AppConstants.fontSizeMedium,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          if (user.verified) ...[
                            const SizedBox(width: 4),
                            const Icon(
                              Icons.verified,
                              size: 16,
                              color: AppConstants.primaryColor,
                            ),
                          ],
                        ],
                      ),
                      Text(
                        '${app_date.AppDateUtils.formatRelativeTime(post.timestamp)}',
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: AppConstants.textLight,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingSmall),

            // 动态内容
            Text(
              post.content,
              style: const TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                color: AppConstants.textPrimary,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),

            const SizedBox(height: AppConstants.paddingSmall),

            // 互动数据
            Row(
              children: [
                _buildInteractionItem(
                  Icons.favorite_border,
                  post.likes,
                  post.liked ? AppConstants.errorColor : AppConstants.textLight,
                ),
                const SizedBox(width: AppConstants.paddingLarge),
                _buildInteractionItem(
                  Icons.chat_bubble_outline,
                  post.comments,
                  AppConstants.textLight,
                ),
                const SizedBox(width: AppConstants.paddingLarge),
                _buildInteractionItem(
                  Icons.share_outlined,
                  post.shares,
                  AppConstants.textLight,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInteractionItem(IconData icon, int count, Color color) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Text(
          AppUtils.formatNumber(count),
          style: TextStyle(
            fontSize: AppConstants.fontSizeSmall,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildUpcomingActivities() {
    if (_upcomingActivities.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '即将到来的活动',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.paddingMedium),
          Column(
            children: _upcomingActivities.map((activity) => _buildActivityItem(activity)).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(Activity activity) {
    return GestureDetector(
      onTap: () => _showActivityDetail(activity),
      child: Container(
        margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // 活动图片
            ClipRRect(
              borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
              child: Image.asset(
                activity.image,
                width: 60,
                height: 60,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                  ),
                  child: const Icon(
                    Icons.event,
                    color: AppConstants.primaryColor,
                    size: 30,
                  ),
                ),
              ),
            ),

            const SizedBox(width: AppConstants.paddingMedium),

            // 活动信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    activity.title,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeMedium,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    activity.description,
                    style: const TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: AppConstants.textSecondary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(
                        Icons.access_time,
                        size: 14,
                        color: AppConstants.textLight,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${app_date.AppDateUtils.formatDate(activity.date, pattern: 'MM月dd日')} ${activity.time}',
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: AppConstants.textLight,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(
                        Icons.location_on,
                        size: 14,
                        color: AppConstants.textLight,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          activity.location,
                          style: const TextStyle(
                            fontSize: AppConstants.fontSizeSmall,
                            color: AppConstants.textLight,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // 报名状态
            Column(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppConstants.paddingSmall,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: activity.hasAvailableSlots
                        ? AppConstants.successColor.withOpacity(0.1)
                        : AppConstants.errorColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    activity.hasAvailableSlots ? '可报名' : '已满员',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeSmall,
                      color: activity.hasAvailableSlots
                          ? AppConstants.successColor
                          : AppConstants.errorColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${activity.participants}/${activity.maxParticipants}',
                  style: const TextStyle(
                    fontSize: AppConstants.fontSizeSmall,
                    color: AppConstants.textLight,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showProductDetail(MenuItem item) {
    context.pushSlideFromRight(ProductDetailPage(product: item));
  }

  void _showPostDetail(CommunityPost post) {
    context.pushSlideFromRight(PostDetailPage(post: post));
  }

  void _showActivityDetail(Activity activity) {
    context.pushSlideFromRight(ActivityDetailPage(activity: activity));
  }
}
