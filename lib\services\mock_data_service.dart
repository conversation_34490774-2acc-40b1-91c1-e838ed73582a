import '../models/user.dart';
import '../models/community_post.dart';
import '../models/menu.dart';
import '../models/order.dart';
import '../models/activity.dart';

/// 模拟数据服务
class MockDataService {
  static final MockDataService _instance = MockDataService._internal();
  factory MockDataService() => _instance;
  MockDataService._internal();

  // 用户数据
  static const List<User> users = [
    User(
      id: 1,
      name: '张小明',
      avatar: 'assets/images/avatar/avatar1.jpg',
      position: '前端工程师',
      location: '中国区',
      verified: true,
      employeeId: 'CN001234',
      joinDate: '2022-03-15',
      points: 1250,
      level: 'Gold',
    ),
    User(
      id: 2,
      name: '<PERSON>',
      avatar: 'assets/images/avatar/avatar2.jpg',
      position: 'Marketing Manager',
      location: '美国区',
      verified: true,
    ),
    User(
      id: 3,
      name: 'วิชัย ใจดี',
      avatar: 'assets/images/avatar/avatar3.jpg',
      position: 'Operations Specialist',
      location: '泰国区',
      verified: false,
    ),
    User(
      id: 4,
      name: '李华',
      avatar: 'assets/images/avatar/avatar4.jpg',
      position: 'HR专员',
      location: '中国区',
      verified: false,
    ),
    User(
      id: 5,
      name: 'Mike Chen',
      avatar: 'assets/images/avatar/avatar5.jpg',
      position: 'Senior Developer',
      location: '美国区',
      verified: true,
    ),
  ];

  // 社区动态数据
  static const List<CommunityPost> communityPosts = [
    CommunityPost(
      id: 1,
      userId: 1,
      content: '今天在咖啡馆尝试了新推出的焦糖玛奇朵，味道真的很棒！推荐给大家 ☕️ #新易盛咖啡馆 #美味推荐',
      images: ['assets/images/community/community1.jpg'],
      timestamp: '2024-01-15 14:30',
      likes: 23,
      comments: 8,
      shares: 3,
      liked: false,
      location: '中国区咖啡馆',
    ),
    CommunityPost(
      id: 2,
      userId: 2,
      content: 'Great coffee chat with the team today! Love how our café brings people together across departments. The atmosphere is perfect for both work and relaxation. 🌟',
      images: ['assets/images/community/community2.jpg'],
      timestamp: '2024-01-15 11:45',
      likes: 45,
      comments: 12,
      shares: 7,
      liked: true,
      location: 'US Office Café',
    ),
    CommunityPost(
      id: 3,
      userId: 3,
      content: 'วันนี้ได้ลองเมนูใหม่ Thai Iced Tea Latte รสชาติดีมาก! ขอบคุณที่นำวัฒนธรรมไทยมาผสมผสานในเมนู 🧡 #ThaiCulture #CoffeeLovers',
      images: ['assets/images/community/community3.jpg'],
      timestamp: '2024-01-15 09:20',
      likes: 31,
      comments: 15,
      shares: 5,
      liked: false,
      location: 'Thailand Office',
    ),
  ];

  // 菜单分类数据
  static const List<MenuCategory> menuCategories = [
    MenuCategory(
      id: 1,
      name: '精品咖啡',
      icon: '☕️',
      items: [
        MenuItem(
          id: 1,
          name: '美式咖啡',
          description: '经典美式，香醇浓郁',
          price: 25,
          originalPrice: 35,
          image: 'assets/images/productCoffee/productCoffee1.jpg',
          images: [
            'assets/images/productCoffee/productCoffee1.jpg',
            'assets/images/productCoffee/productCoffee2.jpg',
            'assets/images/productCoffee/productCoffee3.jpg',
          ],
          hot: true,
          iced: true,
          popular: true,
        ),
        MenuItem(
          id: 2,
          name: '拿铁咖啡',
          description: '香滑奶泡与浓郁咖啡的完美结合',
          price: 30,
          originalPrice: 42,
          image: 'assets/images/productCoffee/productCoffee2.jpg',
          images: [
            'assets/images/productCoffee/productCoffee2.jpg',
            'assets/images/productCoffee/productCoffee4.jpg',
            'assets/images/productCoffee/productCoffee5.jpg',
          ],
          hot: true,
          iced: true,
          popular: true,
        ),
        MenuItem(
          id: 3,
          name: '卡布奇诺',
          description: '经典意式，奶泡丰富',
          price: 28,
          originalPrice: 38,
          image: 'assets/images/productCoffee/productCoffee3.jpg',
          hot: true,
          iced: false,
          popular: false,
        ),
        MenuItem(
          id: 4,
          name: '焦糖玛奇朵',
          description: '香甜焦糖与咖啡的浪漫邂逅',
          price: 35,
          originalPrice: 48,
          image: 'assets/images/productCoffee/productCoffee4.jpg',
          hot: true,
          iced: true,
          popular: true,
        ),
      ],
    ),
    MenuCategory(
      id: 2,
      name: '特色饮品',
      icon: '🥤',
      items: [
        MenuItem(
          id: 5,
          name: '泰式奶茶拿铁',
          description: '融合泰国风味的特色饮品',
          price: 32,
          originalPrice: 45,
          image: 'assets/images/productTea/productTea1.jpg',
          hot: true,
          iced: true,
          popular: true,
          special: '泰国区特色',
        ),
        MenuItem(
          id: 6,
          name: '美式经典奶昔',
          description: '来自美国的经典口味',
          price: 28,
          originalPrice: 38,
          image: 'assets/images/productTea/productTea2.jpg',
          hot: false,
          iced: true,
          popular: false,
          special: '美国区特色',
        ),
      ],
    ),
    MenuCategory(
      id: 3,
      name: '轻食简餐',
      icon: '🥪',
      items: [
        MenuItem(
          id: 7,
          name: '三明治套餐',
          description: '新鲜蔬菜配优质火腿',
          price: 22,
          originalPrice: 32,
          image: 'assets/images/productSnack/productSnack1.jpg',
          popular: true,
        ),
        MenuItem(
          id: 8,
          name: '沙拉碗',
          description: '健康低脂，营养均衡',
          price: 26,
          originalPrice: 35,
          image: 'assets/images/productSnack/productSnack2.jpg',
          popular: false,
        ),
      ],
    ),
  ];

  // 订单数据
  static const List<Order> orders = [
    Order(
      id: 'ORD20240115001',
      status: 'completed',
      statusText: '已完成',
      items: [
        OrderItem(
          id: 1,
          name: '美式咖啡',
          price: 25,
          quantity: 1,
          options: ['热饮', '大杯'],
        ),
        OrderItem(
          id: 7,
          name: '三明治套餐',
          price: 22,
          quantity: 1,
          options: [],
        ),
      ],
      totalAmount: 47,
      orderTime: '2024-01-15 14:30',
      pickupTime: '2024-01-15 14:45',
      pickupLocation: '中国区咖啡馆',
    ),
    Order(
      id: 'ORD20240115002',
      status: 'preparing',
      statusText: '制作中',
      items: [
        OrderItem(
          id: 2,
          name: '拿铁咖啡',
          price: 30,
          quantity: 2,
          options: ['热饮', '中杯'],
        ),
      ],
      totalAmount: 60,
      orderTime: '2024-01-15 15:20',
      estimatedTime: '2024-01-15 15:35',
      pickupLocation: '中国区咖啡馆',
    ),
  ];

  // 活动数据
  static const List<Activity> activities = [
    Activity(
      id: 1,
      title: '跨文化咖啡品鉴会',
      description: '体验来自三个园区的特色咖啡文化',
      date: '2024-01-20',
      time: '14:00-16:00',
      location: '中国区咖啡馆',
      participants: 25,
      maxParticipants: 30,
      image: 'assets/images/activity/activity1.jpg',
      status: 'upcoming',
    ),
    Activity(
      id: 2,
      title: '技术分享下午茶',
      description: '前端开发最新技术趋势分享',
      date: '2024-01-18',
      time: '15:30-17:00',
      location: '中国区咖啡馆',
      participants: 15,
      maxParticipants: 20,
      image: 'assets/images/activity/activity2.jpg',
      status: 'upcoming',
    ),
  ];

  // 获取用户信息
  User? getUserById(int id) {
    try {
      return users.firstWhere((user) => user.id == id);
    } catch (e) {
      return null;
    }
  }

  // 获取当前用户
  User get currentUser => users.first;

  // 获取所有用户
  List<User> getAllUsers() => users;

  // 获取社区动态
  List<CommunityPost> getCommunityPosts() => communityPosts;

  // 获取热门动态
  List<CommunityPost> getHotPosts() {
    return communityPosts
        .where((post) => post.likes > 20)
        .toList()
      ..sort((a, b) => b.likes.compareTo(a.likes));
  }

  // 获取菜单分类
  List<MenuCategory> getMenuCategories() {
    // 收集所有商品
    final allItems = <MenuItem>[];
    for (final category in menuCategories) {
      allItems.addAll(category.items);
    }

    // 创建"全部"分类
    final allCategory = MenuCategory(
      id: 0,
      name: '全部',
      icon: '🍽️',
      items: allItems,
    );

    // 返回包含"全部"分类的列表
    return [allCategory, ...menuCategories];
  }

  // 获取推荐商品
  List<MenuItem> getRecommendedItems() {
    final allItems = <MenuItem>[];
    for (final category in menuCategories) {
      allItems.addAll(category.items);
    }
    return allItems.where((item) => item.popular).toList();
  }

  // 获取订单列表
  List<Order> getOrders() => orders;

  // 根据状态获取订单
  List<Order> getOrdersByStatus(String status) {
    if (status == 'all') return orders;
    return orders.where((order) => order.status == status).toList();
  }

  // 获取活动列表
  List<Activity> getActivities() => activities;

  // 获取即将到来的活动
  List<Activity> getUpcomingActivities() {
    return activities.where((activity) => activity.isUpcoming).toList();
  }
}
