import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../utils/app_utils.dart';

/// 创建动态页面
class CreatePostPage extends StatefulWidget {
  const CreatePostPage({super.key});

  @override
  State<CreatePostPage> createState() => _CreatePostPageState();
}

class _CreatePostPageState extends State<CreatePostPage> {
  final TextEditingController _contentController = TextEditingController();
  final List<String> _selectedImages = [];
  String? _selectedLocation;
  bool _isPublishing = false;

  @override
  void dispose() {
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('发布动态'),
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _isPublishing ? null : _publishPost,
            child: Text(
              '发布',
              style: TextStyle(
                color: _isPublishing ? AppConstants.textLight : AppConstants.primaryColor,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户信息
            _buildUserInfo(),
            
            const SizedBox(height: 20),
            
            // 内容输入框
            _buildContentInput(),
            
            const SizedBox(height: 20),
            
            // 图片选择
            _buildImageSection(),
            
            const SizedBox(height: 20),
            
            // 位置选择
            _buildLocationSection(),
            
            const SizedBox(height: 20),
            
            // 功能选项
            _buildOptionsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildUserInfo() {
    return Row(
      children: [
        CircleAvatar(
          radius: 24,
          backgroundColor: AppConstants.primaryColor,
          child: const Icon(
            Icons.person,
            color: Colors.white,
            size: 24,
          ),
        ),
        
        const SizedBox(width: 12),
        
        const Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '我',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppConstants.textPrimary,
              ),
            ),
            
            Text(
              '分享你的咖啡时光',
              style: TextStyle(
                fontSize: 12,
                color: AppConstants.textSecondary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildContentInput() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: AppConstants.borderColor),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: TextField(
        controller: _contentController,
        maxLines: 8,
        maxLength: 500,
        decoration: const InputDecoration(
          hintText: '分享你的想法...',
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16),
          counterText: '',
        ),
        style: const TextStyle(
          fontSize: 16,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(
              Icons.image_outlined,
              color: AppConstants.textSecondary,
              size: 20,
            ),
            const SizedBox(width: 8),
            const Text(
              '添加图片',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppConstants.textPrimary,
              ),
            ),
            const Spacer(),
            Text(
              '${_selectedImages.length}/9',
              style: const TextStyle(
                fontSize: 12,
                color: AppConstants.textSecondary,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 1,
          ),
          itemCount: _selectedImages.length + (_selectedImages.length < 9 ? 1 : 0),
          itemBuilder: (context, index) {
            if (index == _selectedImages.length) {
              // 添加图片按钮
              return GestureDetector(
                onTap: _pickImage,
                child: Container(
                  decoration: BoxDecoration(
                    color: AppConstants.backgroundColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppConstants.borderColor,
                      style: BorderStyle.solid,
                    ),
                  ),
                  child: const Icon(
                    Icons.add_photo_alternate_outlined,
                    color: AppConstants.textSecondary,
                    size: 32,
                  ),
                ),
              );
            }
            
            // 已选择的图片
            return Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    image: DecorationImage(
                      image: NetworkImage(_selectedImages[index]),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                
                Positioned(
                  top: 4,
                  right: 4,
                  child: GestureDetector(
                    onTap: () => _removeImage(index),
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                        color: Colors.black54,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildLocationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Row(
          children: [
            Icon(
              Icons.location_on_outlined,
              color: AppConstants.textSecondary,
              size: 20,
            ),
            SizedBox(width: 8),
            Text(
              '添加位置',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppConstants.textPrimary,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        GestureDetector(
          onTap: _selectLocation,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppConstants.backgroundColor,
              borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
              border: Border.all(color: AppConstants.borderColor),
            ),
            child: Row(
              children: [
                Icon(
                  _selectedLocation != null 
                      ? Icons.location_on 
                      : Icons.add_location_outlined,
                  color: _selectedLocation != null 
                      ? AppConstants.primaryColor 
                      : AppConstants.textSecondary,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _selectedLocation ?? '选择位置',
                    style: TextStyle(
                      fontSize: 14,
                      color: _selectedLocation != null 
                          ? AppConstants.textPrimary 
                          : AppConstants.textSecondary,
                    ),
                  ),
                ),
                if (_selectedLocation != null)
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedLocation = null;
                      });
                    },
                    child: const Icon(
                      Icons.close,
                      color: AppConstants.textSecondary,
                      size: 16,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOptionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '其他选项',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppConstants.textPrimary,
          ),
        ),
        
        const SizedBox(height: 12),
        
        _buildOptionItem(
          icon: Icons.people_outline,
          title: '提醒朋友',
          subtitle: '在动态中@朋友',
          onTap: _mentionFriends,
        ),
        
        _buildOptionItem(
          icon: Icons.local_offer_outlined,
          title: '添加话题',
          subtitle: '让更多人看到你的动态',
          onTap: _addTopic,
        ),
        
        _buildOptionItem(
          icon: Icons.visibility_outlined,
          title: '谁可以看',
          subtitle: '公开',
          onTap: _setVisibility,
        ),
      ],
    );
  }

  Widget _buildOptionItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Icon(
              icon,
              color: AppConstants.textSecondary,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppConstants.textPrimary,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppConstants.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.chevron_right,
              color: AppConstants.textSecondary,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  void _pickImage() {
    // 模拟选择图片
    if (_selectedImages.length < 9) {
      setState(() {
        _selectedImages.add('https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=400&h=400&fit=crop');
      });
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  void _selectLocation() {
    // 模拟位置选择
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '选择位置',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.location_on),
              title: const Text('新易盛咖啡馆(总店)'),
              subtitle: const Text('北京市朝阳区xxx街道'),
              onTap: () {
                setState(() {
                  _selectedLocation = '新易盛咖啡馆(总店)';
                });
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.location_on),
              title: const Text('新易盛咖啡馆(分店)'),
              subtitle: const Text('北京市海淀区xxx街道'),
              onTap: () {
                setState(() {
                  _selectedLocation = '新易盛咖啡馆(分店)';
                });
                Navigator.pop(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _mentionFriends() {
    AppUtils.showSuccess(context, '@朋友功能开发中...');
  }

  void _addTopic() {
    AppUtils.showSuccess(context, '话题功能开发中...');
  }

  void _setVisibility() {
    AppUtils.showSuccess(context, '隐私设置功能开发中...');
  }

  void _publishPost() async {
    if (_contentController.text.trim().isEmpty) {
      AppUtils.showError(context, '请输入动态内容');
      return;
    }

    setState(() {
      _isPublishing = true;
    });

    // 模拟发布过程
    await Future.delayed(const Duration(seconds: 2));

    if (mounted) {
      setState(() {
        _isPublishing = false;
      });
      
      AppUtils.showSuccess(context, '动态发布成功！');
      
      // 延迟返回
      Future.delayed(const Duration(milliseconds: 1500), () {
        if (mounted) {
          Navigator.of(context).pop(true); // 返回true表示发布成功
        }
      });
    }
  }
}
