import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../services/mock_data_service.dart';
import '../models/community_post.dart';
import '../models/user.dart';
import '../utils/app_utils.dart';
import 'comments_page.dart';

/// 用户动态页面
class UserPostsPage extends StatefulWidget {
  final User user;
  
  const UserPostsPage({
    super.key,
    required this.user,
  });

  @override
  State<UserPostsPage> createState() => _UserPostsPageState();
}

class _UserPostsPageState extends State<UserPostsPage> {
  final MockDataService _dataService = MockDataService();
  List<CommunityPost> _userPosts = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserPosts();
  }

  void _loadUserPosts() {
    setState(() {
      _isLoading = true;
    });

    // 模拟网络延迟
    Future.delayed(const Duration(milliseconds: 500), () {
      setState(() {
        _userPosts = _dataService
            .getCommunityPosts()
            .where((post) => post.userId == widget.user.id)
            .toList();
        _isLoading = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: Text('${widget.user.name}的动态'),
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _userPosts.isEmpty
              ? _buildEmptyState()
              : _buildPostsList(),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.article_outlined,
            size: 64,
            color: AppConstants.textLight,
          ),
          SizedBox(height: 16),
          Text(
            '还没有发布动态',
            style: TextStyle(
              color: AppConstants.textLight,
              fontSize: 16,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '快来分享你的咖啡时光吧～',
            style: TextStyle(
              color: AppConstants.textLight,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostsList() {
    return RefreshIndicator(
      onRefresh: () async {
        _loadUserPosts();
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _userPosts.length,
        itemBuilder: (context, index) {
          final post = _userPosts[index];
          return _buildPostCard(post);
        },
      ),
    );
  }

  Widget _buildPostCard(CommunityPost post) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户信息和时间
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundImage: AssetImage(widget.user.avatar),
                  onBackgroundImageError: (exception, stackTrace) {},
                  child: widget.user.avatar.isEmpty
                      ? const Icon(Icons.person, size: 20)
                      : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.user.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppConstants.textPrimary,
                        ),
                      ),
                      Text(
                        AppUtils.formatTime(DateTime.parse(post.timestamp)),
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppConstants.textLight,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // 动态内容
            Text(
              post.content,
              style: const TextStyle(
                fontSize: 15,
                color: AppConstants.textPrimary,
                height: 1.4,
              ),
            ),

            // 图片（如果有）
            if (post.images.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildImageGrid(post.images),
            ],

            const SizedBox(height: 16),

            // 互动按钮
            Row(
              children: [
                _buildInteractionButton(
                  icon: post.liked ? Icons.favorite : Icons.favorite_border,
                  count: post.likes,
                  color: post.liked ? Colors.red : AppConstants.textLight,
                  onTap: () => _toggleLike(post),
                ),
                const SizedBox(width: 24),
                _buildInteractionButton(
                  icon: Icons.chat_bubble_outline,
                  count: post.comments,
                  color: AppConstants.textLight,
                  onTap: () => _showComments(post),
                ),
                const SizedBox(width: 24),
                _buildInteractionButton(
                  icon: Icons.share_outlined,
                  count: post.shares,
                  color: AppConstants.textLight,
                  onTap: () => _sharePost(post),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageGrid(List<String> images) {
    if (images.length == 1) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.asset(
          images[0],
          width: double.infinity,
          height: 200,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              width: double.infinity,
              height: 200,
              color: Colors.grey[200],
              child: const Icon(Icons.image_not_supported),
            );
          },
        ),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: images.length == 2 ? 2 : 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: images.length > 9 ? 9 : images.length,
      itemBuilder: (context, index) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.asset(
            images[index],
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                color: Colors.grey[200],
                child: const Icon(Icons.image_not_supported),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildInteractionButton({
    required IconData icon,
    required int count,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 20, color: color),
          const SizedBox(width: 4),
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 14,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  void _toggleLike(CommunityPost post) {
    setState(() {
      final index = _userPosts.indexWhere((p) => p.id == post.id);
      if (index != -1) {
        if (post.liked) {
          _userPosts[index] = post.copyWith(
            likes: post.likes - 1,
            liked: false,
          );
        } else {
          _userPosts[index] = post.copyWith(
            likes: post.likes + 1,
            liked: true,
          );
        }
      }
    });
  }

  void _showComments(CommunityPost post) {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => CommentsPage(post: post),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.ease;
          var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
      ),
    );
  }

  void _sharePost(CommunityPost post) {
    AppUtils.showToast(context, '分享功能开发中...');
  }
}
