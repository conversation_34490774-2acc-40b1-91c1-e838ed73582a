import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../services/mock_data_service.dart';
import '../models/menu.dart';
import '../utils/app_utils.dart';
import '../utils/page_transitions.dart';
import '../widgets/product_spec_modal.dart';
import 'product_detail_page.dart';
import 'cart_page.dart';

/// 菜单页面
class MenuPage extends StatefulWidget {
  const MenuPage({super.key});

  @override
  State<MenuPage> createState() => _MenuPageState();
}

class _MenuPageState extends State<MenuPage> with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  final MockDataService _dataService = MockDataService();
  late TabController _tabController;

  List<MenuCategory> _categories = [];
  List<MenuItem> _filteredItems = [];
  String _searchKeyword = '';
  int _selectedCategoryIndex = 0;
  int _cartItemCount = 0;
  Map<String, int> _itemQuantities = {}; // 商品数量管理

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _loadData() {
    setState(() {
      _categories = _dataService.getMenuCategories();
      if (_categories.isNotEmpty) {
        _tabController = TabController(length: _categories.length, vsync: this);
        _filteredItems = _categories[0].items;
      }
    });
  }

  void _onSearch(String keyword) {
    setState(() {
      _searchKeyword = keyword;
      if (keyword.isEmpty) {
        _filteredItems = _categories[_selectedCategoryIndex].items;
      } else {
        _filteredItems = _categories[_selectedCategoryIndex]
            .items
            .where((item) =>
                item.name.toLowerCase().contains(keyword.toLowerCase()) ||
                item.description.toLowerCase().contains(keyword.toLowerCase()))
            .toList();
      }
    });
  }

  void _onCategoryChanged(int index) {
    setState(() {
      _selectedCategoryIndex = index;
      _searchKeyword = '';
      _searchController.clear();
      _filteredItems = _categories[index].items;
    });
  }

  void _filterItems() {
    if (_searchKeyword.isEmpty) {
      _filteredItems = _categories[_selectedCategoryIndex].items;
    } else {
      _filteredItems = _categories[_selectedCategoryIndex]
          .items
          .where((item) =>
              item.name.toLowerCase().contains(_searchKeyword.toLowerCase()))
          .toList();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_categories.isEmpty) {
      return Scaffold(
        backgroundColor: AppConstants.backgroundColor,
        appBar: AppBar(
          title: const Text('菜单'),
          automaticallyImplyLeading: false,
        ),
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text('菜单'),
        automaticallyImplyLeading: false,
        actions: [
          // 搜索按钮
          IconButton(
            onPressed: _showSearchDialog,
            icon: const Icon(Icons.search),
            tooltip: '搜索',
          ),
          // 购物车按钮
          Stack(
            children: [
              IconButton(
                onPressed: _showCartDialog,
                icon: const Icon(Icons.shopping_cart),
                tooltip: '购物车',
              ),
              if (_cartItemCount > 0)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      '$_cartItemCount',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Row(
        children: [
          // 左侧分类栏
          _buildCategorySidebar(),

          // 右侧商品列表
          Expanded(
            child: _buildMenuContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      color: Colors.white,
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: '搜索咖啡、茶饮、小食...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: _onSearch,
            ),
          ),
          const SizedBox(width: AppConstants.paddingMedium),
          Stack(
            children: [
              IconButton(
                onPressed: () {
                  // TODO: 打开购物车
                },
                icon: const Icon(Icons.shopping_cart),
                iconSize: AppConstants.iconSizeLarge,
              ),
              if (_cartItemCount > 0)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: const BoxDecoration(
                      color: AppConstants.errorColor,
                      shape: BoxShape.circle,
                    ),
                    child: Text(
                      _cartItemCount > 99 ? '99+' : _cartItemCount.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMenuContent() {
    if (_filteredItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: AppConstants.textLight,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              _searchKeyword.isEmpty ? '暂无商品' : '未找到相关商品',
              style: const TextStyle(
                color: AppConstants.textLight,
                fontSize: AppConstants.fontSizeLarge,
              ),
            ),
            if (_searchKeyword.isNotEmpty) ...[
              const SizedBox(height: AppConstants.paddingSmall),
              Text(
                '试试搜索其他关键词',
                style: const TextStyle(
                  color: AppConstants.textLight,
                  fontSize: AppConstants.fontSizeMedium,
                ),
              ),
            ],
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingSmall),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 1, // 改为1列布局，每行显示1个商品
        crossAxisSpacing: AppConstants.paddingSmall,
        mainAxisSpacing: AppConstants.paddingSmall,
        childAspectRatio: 2.2, // 调整比例适合单行布局，增加高度
      ),
      itemCount: _filteredItems.length,
      itemBuilder: (context, index) {
        final item = _filteredItems[index];
        return _buildMenuItem(item);
      },
    );
  }

  Widget _buildMenuItem(MenuItem item) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          _showProductDetail(item);
        },
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        child: Row(
          children: [
            // 商品图片
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(AppConstants.radiusMedium),
                bottomLeft: Radius.circular(AppConstants.radiusMedium),
              ),
              child: Image.asset(
                item.image,
                width: 120,
                height: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  width: 120,
                  height: double.infinity,
                  color: AppConstants.backgroundColor,
                  child: const Icon(
                    Icons.local_cafe,
                    color: AppConstants.textLight,
                    size: 40,
                  ),
                ),
              ),
            ),

            // 商品信息
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppConstants.paddingSmall),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // 商品名称和热销标签
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            item.name,
                            style: const TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.w600,
                              color: AppConstants.textPrimary,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (item.popular)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 4,
                              vertical: 1,
                            ),
                            decoration: BoxDecoration(
                              color: AppConstants.primaryColor,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              '热销',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 9,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),

                    // 商品描述
                    Text(
                      item.description,
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppConstants.textSecondary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    // 价格和数量选择
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          AppUtils.formatPrice(item.price),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppConstants.primaryColor,
                          ),
                        ),
                        _buildQuantitySelector(item),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showProductDetail(MenuItem item) {
    context.pushSlideFromRight(ProductDetailPage(product: item));
  }

  Widget _buildQuantitySelector(MenuItem item) {
    // 显示添加按钮
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: AppConstants.primaryColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: IconButton(
        onPressed: () => _addToCart(item),
        icon: const Icon(
          Icons.add,
          color: Colors.white,
          size: 18,
        ),
        padding: EdgeInsets.zero,
      ),
    );
  }

  void _addToCart(MenuItem item) {
    _showProductSpecModal(item);
  }

  void _showProductSpecModal(MenuItem item) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ProductSpecModal(
        product: item,
        onAddToCart: (product, quantity, specs) {
          setState(() {
            final currentQuantity = _itemQuantities[item.id.toString()] ?? 0;
            _itemQuantities[item.id.toString()] = currentQuantity + quantity;
            _cartItemCount += quantity;
          });
          AppUtils.showSuccess(context, '${item.name} x$quantity 已加入购物车');
          AppUtils.vibrate();
        },
      ),
    );
  }

  void _decreaseQuantity(MenuItem item) {
    setState(() {
      final currentQuantity = _itemQuantities[item.id.toString()] ?? 0;
      if (currentQuantity > 0) {
        _itemQuantities[item.id.toString()] = currentQuantity - 1;
        _cartItemCount--;
        if (_itemQuantities[item.id.toString()] == 0) {
          _itemQuantities.remove(item.id.toString());
        }
      }
    });
  }

  Widget _buildCategorySidebar() {
    return Container(
      width: 80,
      color: Colors.grey[50],
      child: ListView.builder(
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = index == _selectedCategoryIndex;

          return GestureDetector(
            onTap: () => _onCategoryChanged(index),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
              decoration: BoxDecoration(
                color: isSelected ? Colors.white : Colors.transparent,
                border: Border(
                  right: BorderSide(
                    color: isSelected
                        ? AppConstants.primaryColor
                        : Colors.transparent,
                    width: 3,
                  ),
                ),
              ),
              child: Column(
                children: [
                  Text(
                    category.icon,
                    style: const TextStyle(fontSize: 20),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    category.name,
                    style: TextStyle(
                      fontSize: 12,
                      color: isSelected
                          ? AppConstants.primaryColor
                          : AppConstants.textSecondary,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('搜索商品'),
        content: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: '请输入商品名称',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
          onSubmitted: (value) {
            Navigator.of(context).pop();
            _performSearch(value);
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _performSearch(_searchController.text);
            },
            child: const Text('搜索'),
          ),
        ],
      ),
    );
  }

  void _performSearch(String keyword) {
    setState(() {
      _searchKeyword = keyword;
      _filterItems();
    });
  }

  void _showCartDialog() {
    context.pushSlideFromRight(const CartPage());
  }
}
