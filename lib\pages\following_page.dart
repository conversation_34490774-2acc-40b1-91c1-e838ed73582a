import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/user.dart';
import '../utils/app_utils.dart';

/// 关注页面
class FollowingPage extends StatefulWidget {
  final User user;
  
  const FollowingPage({
    super.key,
    required this.user,
  });

  @override
  State<FollowingPage> createState() => _FollowingPageState();
}

class _FollowingPageState extends State<FollowingPage> {
  List<FollowingUser> _following = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadFollowing();
  }

  void _loadFollowing() {
    setState(() {
      _isLoading = true;
    });

    // 模拟网络延迟和数据
    Future.delayed(const Duration(milliseconds: 500), () {
      setState(() {
        _following = _generateMockFollowing();
        _isLoading = false;
      });
    });
  }

  List<FollowingUser> _generateMockFollowing() {
    return [
      FollowingUser(
        id: '1',
        name: '新易盛咖啡官方',
        avatar: 'assets/images/logo.png',
        bio: '新易盛咖啡官方账号，为您带来最新资讯',
        isFollowing: true,
        followTime: DateTime.now().subtract(const Duration(days: 100)),
        category: '官方账号',
      ),
      FollowingUser(
        id: '2',
        name: '咖啡大师李师傅',
        avatar: 'assets/images/master1.jpg',
        bio: '20年咖啡制作经验，分享咖啡知识',
        isFollowing: true,
        followTime: DateTime.now().subtract(const Duration(days: 80)),
        category: '咖啡师',
      ),
      FollowingUser(
        id: '3',
        name: '烘焙工坊',
        avatar: 'assets/images/roastery.jpg',
        bio: '专业咖啡豆烘焙，品质保证',
        isFollowing: true,
        followTime: DateTime.now().subtract(const Duration(days: 60)),
        category: '烘焙师',
      ),
      FollowingUser(
        id: '4',
        name: '咖啡文化传播者',
        avatar: 'assets/images/culture.jpg',
        bio: '传播咖啡文化，分享咖啡故事',
        isFollowing: true,
        followTime: DateTime.now().subtract(const Duration(days: 40)),
        category: '文化传播',
      ),
      FollowingUser(
        id: '5',
        name: '拉花艺术家小美',
        avatar: 'assets/images/latte_art.jpg',
        bio: '拉花艺术创作，每一杯都是艺术品',
        isFollowing: true,
        followTime: DateTime.now().subtract(const Duration(days: 25)),
        category: '拉花师',
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: Text('${widget.user.name}的关注'),
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _following.isEmpty
              ? _buildEmptyState()
              : _buildFollowingList(),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_add_outlined,
            size: 64,
            color: AppConstants.textLight,
          ),
          SizedBox(height: 16),
          Text(
            '还没有关注任何人',
            style: TextStyle(
              color: AppConstants.textLight,
              fontSize: 16,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '去发现更多有趣的人吧～',
            style: TextStyle(
              color: AppConstants.textLight,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFollowingList() {
    return RefreshIndicator(
      onRefresh: () async {
        _loadFollowing();
      },
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _following.length,
        itemBuilder: (context, index) {
          final following = _following[index];
          return _buildFollowingCard(following);
        },
      ),
    );
  }

  Widget _buildFollowingCard(FollowingUser following) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // 头像
            CircleAvatar(
              radius: 25,
              backgroundImage: AssetImage(following.avatar),
              onBackgroundImageError: (exception, stackTrace) {},
              child: following.avatar.isEmpty
                  ? const Icon(Icons.person, size: 25)
                  : null,
            ),
            
            const SizedBox(width: 12),
            
            // 用户信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 用户名称（不换行，超出截断）
                  Text(
                    following.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppConstants.textPrimary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 4),

                  // 标签放在下面一行
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: AppConstants.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      following.category,
                      style: const TextStyle(
                        fontSize: 10,
                        color: AppConstants.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    following.bio,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppConstants.textSecondary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '关注时间：${AppUtils.formatTime(following.followTime)}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppConstants.textLight,
                    ),
                  ),
                ],
              ),
            ),
            
            // 取消关注按钮
            _buildUnfollowButton(following),
          ],
        ),
      ),
    );
  }

  Widget _buildUnfollowButton(FollowingUser following) {
    return GestureDetector(
      onTap: () => _showUnfollowDialog(following),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: const Text(
          '已关注',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppConstants.textSecondary,
          ),
        ),
      ),
    );
  }

  void _showUnfollowDialog(FollowingUser following) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('取消关注'),
        content: Text('确定要取消关注 ${following.name} 吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _unfollow(following);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _unfollow(FollowingUser following) {
    setState(() {
      _following.remove(following);
    });
    
    AppUtils.showSuccess(context, '已取消关注${following.name}');
  }
}

/// 关注用户模型
class FollowingUser {
  final String id;
  final String name;
  final String avatar;
  final String bio;
  final bool isFollowing;
  final DateTime followTime;
  final String category;

  FollowingUser({
    required this.id,
    required this.name,
    required this.avatar,
    required this.bio,
    required this.isFollowing,
    required this.followTime,
    required this.category,
  });
}
