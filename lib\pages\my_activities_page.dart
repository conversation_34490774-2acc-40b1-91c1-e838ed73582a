import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/activity.dart';
import '../services/mock_data_service.dart';
import '../utils/app_utils.dart';

/// 我的活动页面
class MyActivitiesPage extends StatefulWidget {
  const MyActivitiesPage({super.key});

  @override
  State<MyActivitiesPage> createState() => _MyActivitiesPageState();
}

class _MyActivitiesPageState extends State<MyActivitiesPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final MockDataService _dataService = MockDataService();
  List<Activity> _participatedActivities = [];
  List<Activity> _completedActivities = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadActivities();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadActivities() {
    final allActivities = _dataService.getActivities();
    
    setState(() {
      // 模拟用户参与的活动（进行中）
      _participatedActivities = allActivities.where((activity) => 
        activity.status == '进行中' && activity.id % 3 == 0
      ).toList();
      
      // 模拟用户完成的活动
      _completedActivities = allActivities.where((activity) => 
        activity.status == '已结束' && activity.id % 2 == 0
      ).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text('我的活动'),
        backgroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppConstants.primaryColor,
          unselectedLabelColor: AppConstants.textSecondary,
          indicatorColor: AppConstants.primaryColor,
          tabs: [
            Tab(text: '参与中 (${_participatedActivities.length})'),
            Tab(text: '已完成 (${_completedActivities.length})'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildActivityList(_participatedActivities, true),
          _buildActivityList(_completedActivities, false),
        ],
      ),
    );
  }

  Widget _buildActivityList(List<Activity> activities, bool isParticipating) {
    if (activities.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isParticipating ? Icons.event_busy : Icons.history,
              size: 80,
              color: AppConstants.textLight,
            ),
            const SizedBox(height: 16),
            Text(
              isParticipating ? '暂无参与中的活动' : '暂无完成的活动',
              style: const TextStyle(
                fontSize: 16,
                color: AppConstants.textLight,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isParticipating ? '快去参加精彩活动吧！' : '完成活动可获得丰厚奖励',
              style: const TextStyle(
                fontSize: 14,
                color: AppConstants.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _loadActivities();
      },
      child: ListView.builder(
        padding: const EdgeInsets.only(
          top: AppConstants.paddingLarge,
          bottom: AppConstants.paddingLarge,
          left: AppConstants.paddingSmall,
          right: AppConstants.paddingSmall
        ),
        itemCount: activities.length,
        itemBuilder: (context, index) {
          final activity = activities[index];
          return _buildActivityItem(activity, isParticipating);
        },
      ),
    );
  }

  Widget _buildActivityItem(Activity activity, bool isParticipating) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 活动图片
          ClipRRect(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(AppConstants.radiusLarge)),
            child: Image.asset(
              activity.image,
              height: 150,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: 150,
                  color: AppConstants.backgroundColor,
                  child: const Icon(
                    Icons.image_not_supported,
                    size: 50,
                    color: AppConstants.textLight,
                  ),
                );
              },
            ),
          ),
          
          Padding(
            padding: const EdgeInsets.all(AppConstants.paddingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 活动标题和状态
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        activity.title,
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeLarge,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.textPrimary,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: isParticipating 
                            ? AppConstants.primaryColor.withValues(alpha: 0.1)
                            : Colors.grey.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        isParticipating ? '进行中' : '已完成',
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: isParticipating ? AppConstants.primaryColor : Colors.grey,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 8),
                
                // 活动描述
                Text(
                  activity.description,
                  style: const TextStyle(
                    fontSize: AppConstants.fontSizeMedium,
                    color: AppConstants.textSecondary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                
                const SizedBox(height: 12),
                
                // 活动信息
                Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      size: 16,
                      color: AppConstants.textLight,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${activity.date} ${activity.time}',
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                        color: AppConstants.textLight,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 8),
                
                Row(
                  children: [
                    Icon(
                      Icons.card_giftcard,
                      size: 16,
                      color: AppConstants.textLight,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '地点：${activity.location}',
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                        color: AppConstants.textLight,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // 互动按钮
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildInteractionButton(
                      icon: Icons.people,
                      count: activity.participants,
                      label: '参与人数',
                      color: AppConstants.textLight,
                      onTap: () => _showParticipants(activity),
                    ),
                    _buildInteractionButton(
                      icon: Icons.info_outline,
                      count: 0,
                      label: '详情',
                      color: AppConstants.textLight,
                      onTap: () => _showActivityDetail(activity),
                    ),
                    _buildInteractionButton(
                      icon: Icons.share_outlined,
                      count: 0,
                      label: '分享',
                      color: AppConstants.textLight,
                      onTap: () => _shareActivity(activity),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _viewActivityDetail(Activity activity) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(activity.title),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '活动描述',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Text(activity.description),
              const SizedBox(height: 16),
              Text(
                '活动时间',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Text('${activity.date} ${activity.time}'),
              const SizedBox(height: 16),
              Text(
                '活动奖励',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Text('地点：${activity.location}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _continueActivity(Activity activity) {
    AppUtils.showToast(context, '继续参与活动：${activity.title}');
  }

  void _viewReward(Activity activity) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('活动奖励'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.card_giftcard,
              size: 60,
              color: AppConstants.primaryColor,
            ),
            const SizedBox(height: 16),
            Text(
              '恭喜您完成活动！',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '活动地点：${activity.location}',
              style: const TextStyle(
                fontSize: 16,
                color: AppConstants.primaryColor,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  Widget _buildInteractionButton({
    required IconData icon,
    required int count,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingSmall,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 20, color: color),
            const SizedBox(width: 4),
            Text(
              count > 0 ? count.toString() : label,
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showActivityDetail(Activity activity) {
    _viewActivityDetail(activity);
  }

  void _showParticipants(Activity activity) {
    AppUtils.showToast(context, '参与人数：${activity.participants}/${activity.maxParticipants}');
  }

  void _shareActivity(Activity activity) {
    AppUtils.showToast(context, '分享活动：${activity.title}');
  }
}
