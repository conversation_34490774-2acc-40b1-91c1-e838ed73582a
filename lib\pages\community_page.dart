import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../services/mock_data_service.dart';
import '../services/app_service.dart';
import '../models/community_post.dart';
import '../utils/date_utils.dart' as app_date;
import '../utils/app_utils.dart';
import '../utils/page_transitions.dart';
import 'post_detail_page.dart';
import 'create_post_page.dart';

/// 社区页面
class CommunityPage extends StatefulWidget {
  const CommunityPage({super.key});

  @override
  State<CommunityPage> createState() => _CommunityPageState();
}

class _CommunityPageState extends State<CommunityPage> with TickerProviderStateMixin {
  late TabController _tabController;
  final MockDataService _dataService = MockDataService();
  bool _showSearch = false;
  AppService? _appService;
  final TextEditingController _searchController = TextEditingController();

  // 本地状态管理
  Map<int, bool> _likedPosts = {}; // 记录点赞状态
  Map<int, int> _likeCounts = {}; // 记录点赞数量

  final List<Map<String, String>> _tabs = [
    {'key': 'all', 'name': '全部'},
    {'key': 'following', 'name': '关注'},
    {'key': 'hot', 'name': '热门'},
    {'key': 'nearby', 'name': '主题'},
  ];

  List<CommunityPost> _allPosts = [];
  List<CommunityPost> _filteredPosts = [];
  String _currentTab = 'all';
  String _searchKeyword = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    // 为TabController添加一个监听器，使其成为状态更新的唯一来源
    _tabController.addListener(_handleTabSelection);
    _loadPosts();
    _initializeLikeStates();

    // 在第一帧后执行，以确保context可用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 获取AppService实例并添加监听器
      _appService = context.read<AppService>();
      _appService?.addListener(_handleTabChangeFromService);
      // 立即处理一次，以防在页面加载前就已经设置了索引
      _handleTabChangeFromService();
    });
  }

  // 当TabController的索引改变时（无论是用户点击还是编程方式），此方法被调用
  void _handleTabSelection() {
    // 确保我们只在tab切换完成时更新状态
    if (!_tabController.indexIsChanging) {
      final newTab = _tabs[_tabController.index]['key']!;
      if (_currentTab != newTab) {
        setState(() {
          _currentTab = newTab;
          _filterPosts();
        });
      }
    }
  }

  void _handleTabChangeFromService() {
    if (!mounted || _appService == null) return;
    final targetIndex = _appService!.communityTabIndex;
    if (targetIndex != 0 && _tabController.index != targetIndex) {
      // 直接设置索引。这会触发上面添加的_handleTabSelection监听器，从而更新UI。
      _tabController.index = targetIndex;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && _appService?.communityTabIndex != 0) {
          _appService?.setCommunityTabIndex(0);
        }
      });
    }
  }

  void _initializeLikeStates() {
    // 初始化点赞状态
    for (final post in _allPosts) {
      _likedPosts[post.id] = post.liked;
      _likeCounts[post.id] = post.likes;
    }
  }

  @override
  void dispose() {
    // 移除监听器以防止内存泄漏
    _appService?.removeListener(_handleTabChangeFromService);
    _tabController.removeListener(_handleTabSelection);
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _loadPosts() {
    setState(() {
      _allPosts = _dataService.getCommunityPosts();
      _filterPosts();
    });
  }

  void _filterPosts() {
    List<CommunityPost> tempPosts;

    // 根据标签筛选
    switch (_currentTab) {
      case 'hot':
        tempPosts = _dataService.getHotPosts();
        break;
      case 'following':
        // TODO: 实现关注用户的动态筛选
        tempPosts = _allPosts.take(2).toList();
        break;
      case 'nearby':
        // TODO: 实现附近动态筛选
        tempPosts = _allPosts.where((post) => post.location?.contains('中国区') == true).toList();
        break;
      default:
        tempPosts = List.from(_allPosts);
    }

    // 根据搜索关键词筛选
    if (_searchKeyword.isNotEmpty) {
      tempPosts = tempPosts.where((post) =>
          post.content.toLowerCase().contains(_searchKeyword.toLowerCase())).toList();
    }

    // The calling method will handle setState
    _filteredPosts = tempPosts;
  }

  void _onSearch(String keyword) {
    setState(() {
      _searchKeyword = keyword;
      _filterPosts();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text('社区'),
        automaticallyImplyLeading: false,
        actions: [
          if (_showSearch) ...[
            Container(
              width: 200,
              margin: const EdgeInsets.symmetric(vertical: 8),
              child: TextField(
                controller: _searchController,
                autofocus: true,
                decoration: InputDecoration(
                  hintText: '搜索动态内容...',
                  prefixIcon: const Icon(Icons.search, size: 20),
                  suffixIcon: IconButton(
                    onPressed: () {
                      setState(() {
                        _showSearch = false;
                        _searchController.clear();
                        _searchKeyword = '';
                        _filterPosts();
                      });
                    },
                    icon: const Icon(Icons.close, size: 20),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.grey[100],
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  isDense: true,
                ),
                onChanged: _onSearch,
              ),
            ),
          ] else ...[
            IconButton(
              onPressed: () {
                setState(() {
                  _showSearch = true;
                });
              },
              icon: const Icon(Icons.search),
            ),
            IconButton(
              onPressed: () {
                context.pushSlideFromRight(const CreatePostPage());
              },
              icon: const Icon(Icons.edit),
            ),
          ],
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48),
          child: TabBar(
            controller: _tabController,
            // onTap回调不再需要，因为我们使用了controller的监听器
            tabs: _tabs.map((tab) => Tab(text: tab['name'])).toList(),
          ),
        ),
      ),
      body: _buildPostList(),
    );
  }

  Widget _buildPostList() {
    if (_filteredPosts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.forum_outlined,
              size: 64,
              color: AppConstants.textLight,
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            Text(
              _searchKeyword.isEmpty ? '暂无动态' : '未找到相关动态',
              style: const TextStyle(
                color: AppConstants.textLight,
                fontSize: AppConstants.fontSizeLarge,
              ),
            ),
            if (_searchKeyword.isNotEmpty) ...[
              const SizedBox(height: AppConstants.paddingSmall),
              Text(
                '试试搜索其他关键词',
                style: const TextStyle(
                  color: AppConstants.textLight,
                  fontSize: AppConstants.fontSizeMedium,
                ),
              ),
            ],
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _loadPosts();
      },
      child: ListView.builder(
        padding: const EdgeInsets.only(top:AppConstants.paddingLarge, bottom:AppConstants.paddingLarge, left: AppConstants.paddingSmall, right: AppConstants.paddingSmall),
        itemCount: _filteredPosts.length,
        itemBuilder: (context, index) {
          final post = _filteredPosts[index];
          return _buildPostItem(post);
        },
      ),
    );
  }

  Widget _buildPostItem(CommunityPost post) {
    final user = _dataService.getUserById(post.userId);
    if (user == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          _showPostDetail(post);
        },
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 用户信息行
              Row(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundImage: AssetImage(user.avatar),
                    onBackgroundImageError: (exception, stackTrace) {},
                    child: user.avatar.isEmpty
                        ? const Icon(Icons.person, size: 24)
                        : null,
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              user.name,
                              style: const TextStyle(
                                fontSize: AppConstants.fontSizeLarge,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            if (user.verified) ...[
                              const SizedBox(width: 4),
                              const Icon(
                                Icons.verified,
                                size: 16,
                                color: AppConstants.primaryColor,
                              ),
                            ],
                          ],
                        ),
                        Row(
                          children: [
                            Text(
                              app_date.AppDateUtils.formatRelativeTime(post.timestamp),
                              style: const TextStyle(
                                fontSize: AppConstants.fontSizeSmall,
                                color: AppConstants.textLight,
                              ),
                            ),
                            if (post.location != null) ...[
                              const SizedBox(width: 8),
                              Icon(
                                Icons.location_on,
                                size: 12,
                                color: AppConstants.textLight,
                              ),
                              const SizedBox(width: 2),
                              Text(
                                post.location!,
                                style: const TextStyle(
                                  fontSize: AppConstants.fontSizeSmall,
                                  color: AppConstants.textLight,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      _showPostOptions(post);
                    },
                    icon: const Icon(
                      Icons.more_horiz,
                      color: AppConstants.textLight,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: AppConstants.paddingMedium),

              // 动态内容
              Text(
                post.content,
                style: const TextStyle(
                  fontSize: AppConstants.fontSizeLarge,
                  color: AppConstants.textPrimary,
                  height: 1.4,
                ),
              ),

              // 图片（如果有）
              if (post.images.isNotEmpty) ...[
                const SizedBox(height: AppConstants.paddingMedium),
                _buildPostImages(post.images),
              ],

              const SizedBox(height: AppConstants.paddingMedium),

              // 互动按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildInteractionButton(
                    icon: (_likedPosts[post.id] ?? post.liked) ? Icons.favorite : Icons.favorite_border,
                    count: _likeCounts[post.id] ?? post.likes,
                    color: (_likedPosts[post.id] ?? post.liked) ? AppConstants.errorColor : AppConstants.textLight,
                    onTap: () => _toggleLike(post),
                  ),
                  _buildInteractionButton(
                    icon: Icons.chat_bubble_outline,
                    count: post.comments,
                    color: AppConstants.textLight,
                    onTap: () => _showComments(post),
                  ),
                  _buildInteractionButton(
                    icon: Icons.share_outlined,
                    count: post.shares,
                    color: AppConstants.textLight,
                    onTap: () => _sharePost(post),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPostImages(List<String> images) {
    if (images.length == 1) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
        child: Image.asset(
          images[0],
          width: double.infinity,
          height: 200,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => Container(
            width: double.infinity,
            height: 200,
            color: AppConstants.backgroundColor,
            child: const Icon(
              Icons.image_not_supported,
              color: AppConstants.textLight,
              size: 40,
            ),
          ),
        ),
      );
    }

    // 多张图片的网格布局
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: images.length > 4 ? 4 : images.length,
      itemBuilder: (context, index) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
          child: Stack(
            children: [
              Image.asset(
                images[index],
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  color: AppConstants.backgroundColor,
                  child: const Icon(
                    Icons.image_not_supported,
                    color: AppConstants.textLight,
                    size: 30,
                  ),
                ),
              ),
              if (index == 3 && images.length > 4)
                Container(
                  color: Colors.black.withOpacity(0.5),
                  child: Center(
                    child: Text(
                      '+${images.length - 4}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: AppConstants.fontSizeLarge,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInteractionButton({
    required IconData icon,
    required int count,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: AppConstants.paddingMedium,
          vertical: AppConstants.paddingSmall,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 20, color: color),
            const SizedBox(width: 4),
            Text(
              AppUtils.formatNumber(count),
              style: TextStyle(
                fontSize: AppConstants.fontSizeMedium,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showPostDetail(CommunityPost post) {
    context.pushSlideFromRight(PostDetailPage(post: post));
  }

  void _showPostOptions(CommunityPost post) {
    AppUtils.showToast(context, '更多选项功能开发中...');
  }

  void _toggleLike(CommunityPost post) {
    setState(() {
      final currentLiked = _likedPosts[post.id] ?? post.liked;
      final currentCount = _likeCounts[post.id] ?? post.likes;

      _likedPosts[post.id] = !currentLiked;
      _likeCounts[post.id] = currentLiked ? currentCount - 1 : currentCount + 1;
    });
    AppUtils.vibrate();
    AppUtils.showToast(context, _likedPosts[post.id]! ? '已点赞' : '已取消点赞');
  }

  void _showComments(CommunityPost post) {
    AppUtils.showToast(context, '评论功能开发中...');
  }

  void _sharePost(CommunityPost post) {
    AppUtils.showToast(context, '分享功能开发中...');
  }
}
