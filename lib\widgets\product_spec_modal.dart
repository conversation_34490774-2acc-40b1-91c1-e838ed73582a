import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/menu.dart';
import '../utils/app_utils.dart';

/// 商品规格选择弹出层
class ProductSpecModal extends StatefulWidget {
  final MenuItem product;
  final Function(MenuItem, int, Map<String, String>) onAddToCart;

  const ProductSpecModal({
    super.key,
    required this.product,
    required this.onAddToCart,
  });

  @override
  State<ProductSpecModal> createState() => _ProductSpecModalState();
}

class _ProductSpecModalState extends State<ProductSpecModal> {
  int _quantity = 1;
  String _selectedSize = '中杯';
  String _selectedTemperature = '热饮';
  String _selectedSweetness = '标准';

  final List<String> _sizes = ['小杯', '中杯', '大杯', '超大杯'];
  final List<String> _temperatures = ['热饮', '温饮', '冰饮'];
  final List<String> _sweetness = ['无糖', '少糖', '标准', '多糖'];

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顶部拖拽指示器
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                margin: const EdgeInsets.only(top: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ],
          ),

          // 商品信息
          _buildProductInfo(),

          // 规格选择
          _buildSpecifications(),

          // 数量选择和加入购物车
          _buildBottomActions(),
        ],
      ),
    );
  }

  Widget _buildProductInfo() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // 商品图片
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.asset(
              widget.product.image,
              width: 80,
              height: 80,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                width: 80,
                height: 80,
                color: AppConstants.backgroundColor,
                child: const Icon(
                  Icons.local_cafe,
                  color: AppConstants.textLight,
                  size: 40,
                ),
              ),
            ),
          ),

          const SizedBox(width: 16),

          // 商品信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.product.name,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  widget.product.description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppConstants.textSecondary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Text(
                  AppUtils.formatPrice(widget.product.price),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpecifications() {
    return Container(
      padding: const EdgeInsets.only(left: 20, right: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 规格选择标题与商品图片左对齐
          Padding(
            padding: const EdgeInsets.only(left: 0), // 与商品图片左边缘对齐
            child: const Text(
              '规格选择',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppConstants.textPrimary,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // 规格选项也与商品图片左对齐
          Padding(
            padding: const EdgeInsets.only(left: 0), // 与商品图片左边缘对齐
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 杯型选择
                _buildSpecOption('杯型', _sizes, _selectedSize, (value) {
                  setState(() {
                    _selectedSize = value;
                  });
                }),

                const SizedBox(height: 16),

                // 温度选择
                if ((widget.product.hot ?? false) ||
                    (widget.product.iced ?? false))
                  Column(
                    children: [
                      _buildSpecOption(
                          '温度', _temperatures, _selectedTemperature, (value) {
                        setState(() {
                          _selectedTemperature = value;
                        });
                      }),
                      const SizedBox(height: 16),
                    ],
                  ),

                // 甜度选择
                _buildSpecOption('甜度', _sweetness, _selectedSweetness, (value) {
                  setState(() {
                    _selectedSweetness = value;
                  });
                }),
              ],
            ),
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildSpecOption(String title, List<String> options, String selected,
      Function(String) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppConstants.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: options.map((option) {
            final isSelected = option == selected;
            return GestureDetector(
              onTap: () => onChanged(option),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: isSelected ? AppConstants.primaryColor : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: isSelected
                        ? AppConstants.primaryColor
                        : Colors.grey.shade300,
                  ),
                ),
                child: Text(
                  option,
                  style: TextStyle(
                    fontSize: 12,
                    color: isSelected ? Colors.white : AppConstants.textPrimary,
                    fontWeight:
                        isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 数量选择器
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed: _quantity > 1
                        ? () {
                            setState(() {
                              _quantity--;
                            });
                          }
                        : null,
                    icon: const Icon(Icons.remove, size: 18),
                    constraints:
                        const BoxConstraints(minWidth: 36, minHeight: 36),
                  ),
                  Container(
                    width: 40,
                    alignment: Alignment.center,
                    child: Text(
                      '$_quantity',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _quantity++;
                      });
                    },
                    icon: const Icon(Icons.add, size: 18),
                    constraints:
                        const BoxConstraints(minWidth: 36, minHeight: 36),
                  ),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // 加入购物车按钮
            Expanded(
              child: ElevatedButton(
                onPressed: () {
                  final specs = {
                    '杯型': _selectedSize,
                    '温度': _selectedTemperature,
                    '甜度': _selectedSweetness,
                  };
                  widget.onAddToCart(widget.product, _quantity, specs);
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppConstants.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  '加入购物车 ${AppUtils.formatPrice(widget.product.price * _quantity)}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
